import { Chip, Input, Select, SelectProps } from "@heroui/react";
import { FormikProps, getIn } from "formik";
import React, { ReactNode, useEffect } from "react";

import { classNames } from "@/constant/constant";
import { NestedKeyOf } from "@/libs/nested-key";

type FormSelectProps<T> = {
  name: NestedKeyOf<T>;
  formik: FormikProps<T>;
  showSeachbar?: boolean;
  children?: ReactNode;
  onFilter:(value: React.SetStateAction<string>) => void
  isCustomizing?: boolean
  isLoadig? : boolean
  search: string
} & SelectProps;

export function SelectSearchAsync<T>({
  name,
  formik,
  children,
  selectionMode,
  showSeachbar = false,
  onChange,
  onFilter,
  isCustomizing = false,
  isLoadig = false,
  search,
  ...res
}: FormSelectProps<T>) {

  const error = getIn(formik.errors, name);
  const touched = getIn(formik.touched, name);
  let value = getIn(formik.values, name) as string | string[];

  const isInValid = !!(error && touched);

  useEffect(() => {
    if (!formik.isSubmitting) return;
    formik.setFieldTouched(name, true, true);
  }, [formik.isSubmitting]);
  

  return (
    <Select
      isLoading={isLoadig}
      classNames={{
        ...classNames.input,
        innerWrapper: "overflow-hidden",
      }}
      color={isInValid ? "danger" : "default"}
      errorMessage={error?.toString()}
      isInvalid={isInValid}
      {...formik.getFieldProps(name)}
      isMultiline={selectionMode === "multiple"}
      listboxProps={
        showSeachbar
          ? {
              topContent: (
                <div className="sticky top-0 z-10 bg-white  py-2">
                  <Input
                    placeholder="Cari Pilihan..."
                    value={search}
                    onChange={(e) => onFilter(e.target.value)} // Update nilai pencarian
                  />
                </div>
              ),
            }
          : undefined
      }
      renderValue={
        selectionMode === "multiple"
          ? (items) => {
              return (
                <div className="flex flex-wrap gap-1 ">
                  {items.map((item) => (
                    <Chip
                      key={item.key}
                      classNames={{
                        content: "max-w-32 line-clamp-1 ",
                      }}
                      color="primary"
                      size="sm"
                      variant="flat"
                      onClose={() => {
                        formik.setFieldValue(
                          name,
                          items
                            .filter((i) => i.key !== item.key)
                            .map((i) => i.key)
                        );
                      }}
                    >
                      {item.textValue}
                    </Chip>
                  ))}
                </div>
              );
            }
          : isCustomizing  ? 
            (items) => {
              return items.map((item) => <p>{item?.textValue}</p> )
            }
          : undefined
      }
      selectedKeys={typeof value === "string" ? [value] : value}
      selectionMode={selectionMode}
      onChange={(e) => {
        if (onChange) onChange(e);
        if (selectionMode === "multiple") {
          formik.setFieldValue(name, e.target.value.split(","));
        } else {
          formik.setFieldValue(name, e.target.value);
        }
      }}
      onClose={() => onFilter("")} // Reset pencarian saat blur
      {...res}
    >
      {children}
    </Select>
  );
}
