import * as yup from "yup";

export const formAdjusment = yup.object({
  dateAdjustment: yup
    .mixed()
    .nullable()
    .required("Tanggal penyesuian wajib diisi"),
  noBatch: yup.string().required("No. Batch wajib diisi"),
  TotalReduced: yup.number().required("Jumlah barang wajib diisi"),
  reason: yup.string().required("Alasan wajib diisi"),
  description: yup.string().required("Keterangan wajib diisi"),
  attachment: yup.string().required("Lampiran wajib diisi"),
});

export type formAdjusment = yup.InferType<typeof formAdjusment>;
