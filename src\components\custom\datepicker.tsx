import {
  DatePicker,
  DatePickerProps,
  DateRangePickerProps,
} from "@heroui/react";
import { DateValue } from "@internationalized/date";
import { I18nProvider } from "@react-aria/i18n";
import { Button } from "@heroui/react";
import { FormikProps, getIn } from "formik";
import { XIcon } from "lucide-react";
import { debounce } from "lodash";

import { classNames } from "@/constant/constant";
import { NestedKeyOf } from "@/libs/nested-key";

type Props<T> = {
  name: keyof T;
  formik: FormikProps<T>;
  submitOnChange?: boolean;
} & DatePickerProps;

export function FilterDatePicker<T>({
  name,
  formik,
  submitOnChange,
  ...res
}: Props<T>) {
  const error = getIn(formik.errors, name);
  const touched = getIn(formik.touched, name);

  const isInValid = !!(touched && error);

  const debounceSubmit = debounce(() => {
    formik.validateForm().then(() => formik.submitForm());
  }, 5000);

  const resProps = { ...res } as any;

  return (
    <I18nProvider locale="id-ID">
      <DatePicker
        CalendarBottomContent={
          <div className="px-3 pb-2 pt-3 inline-flex justify-end w-full">
            <Button
              size="sm"
              variant="flat"
              onPress={() => {
                formik.setFieldValue(name, null);
                formik.validateForm().then(() => formik.submitForm());
              }}
            >
              <XIcon className="h-3 w-3" /> Reset
            </Button>
          </div>
        }
        classNames={{ ...classNames.input }}
        color={isInValid ? "danger" : "default"}
        errorMessage={error}
        isInvalid={isInValid}
        {...formik.getFieldProps(name)}
        calendarProps={{
          onChange: (value) => {
            formik.setFieldValue(name, value ?? null);
            formik.validateForm().then(() => formik.submitForm());
          },
        }}
        onChange={(value) => {
          formik.setFieldValue(name, value ?? null);
          if (submitOnChange) {
            debounceSubmit();
          }
        }}
        {...resProps}
      />
    </I18nProvider>
  );
}

type FormInputProps<T> = {
  name: NestedKeyOf<T>;
  formik: FormikProps<T>;
  submitOnEnter?: boolean;
  onChange?: (value: DateValue | null) => void;
} & DatePickerProps;

export function FormDatePicker<T extends Record<string, any>>({
  name,
  formik,
  onChange,
  ...res
}: FormInputProps<T>) {
  const error = getIn(formik.errors, name);
  const touched = getIn(formik.touched, name);
  const value = getIn(formik.values, name);
  const isInValid = !!(touched && error);

  const resProps = { ...res } as any;

  return (
    <I18nProvider locale="id-ID">
      <DatePicker
        classNames={classNames.input}
        color={isInValid ? "danger" : "default"}
        errorMessage={error}
        isInvalid={isInValid}
        value={value}
        onChange={(val: any) => {
          if (onChange) onChange(val);
          formik.setFieldValue(name, val);
        }}
        {...resProps}
      />
    </I18nProvider>
  );
}

type RangeProps = {
  name?: string[];
  formik: any;
  submitOnChange?: boolean;
} & DateRangePickerProps;
