"use client";

import UTable from "@/components/custom/table";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from "@heroui/react";
import { Trash } from "lucide-react";
import { useCallback } from "react";
import { subParameter } from "./formAddLayanan";

export const users = [];

const columns = [
  {
    key: "jenis",
    label: "Jenis <PERSON>emeri<PERSON>",
  },
  {
    key: "nilai",
    label: "Nilai Rujukan",
  },
  {
    key: "satuan",
    label: "Satuan",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

type Props = {
  data: subParameter[];
  deleteSubParameter: ({
    parameterId,
    subParamId,
  }: {
    parameterId: string;
    subParamId: string;
  }) => void;
  idParameter: string;
};

export default function TableParameter({
  data,
  deleteSubParameter,
  idParameter,
}: Props) {
  const renderCell = useCallback(
    (category: any, column_key: any, index: number) => {
      switch (column_key) {
        case "no":
          return index + 1;

        case "action":
          return (
            <div>
              <Button
                isIconOnly
                color="danger"
                size="sm"
                onPress={() =>
                  deleteSubParameter({
                    parameterId: idParameter,
                    subParamId: category.id,
                  })
                }
              >
                <Trash className="w-4" />
              </Button>
            </div>
          );
        default:
          return category[column_key];
      }
    },
    []
  );

  return (
    <div className="bg-default-50  rounded-md ">
      <UTable bordered={false} parameter={true}>
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              align={
                column.key === "action" || column.key === "status"
                  ? "end"
                  : "start"
              }
              key={column.key}
            >
              {column.label}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody
          items={data}
          emptyContent="No rows to display"
          loadingContent={<Spinner />}
          isLoading={false}
        >
          {data.map((item, index) => (
            <TableRow key={index}>
              {(columnKey) => (
                <TableCell>{renderCell(item, columnKey, index)}</TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </UTable>
    </div>
  );
}
