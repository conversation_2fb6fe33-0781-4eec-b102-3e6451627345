import { getSsotok } from "@/libs/get-ssotok";

import { createAuthCommunicator, SsoResponse } from "@psi/sso-auth";

type SSOProps = {
  error: boolean;
  token: string;
  code: number;
  message: string;
} & (ErrorProps | SuccessProps);

type SuccessProps = {
  error: false;
  data: SsoResponse;
};

type ErrorProps = {
  error: true;
  data: null;
};

export async function SSOAuthentication(): Promise<SSOProps> {
  const ssotok = await getSsotok();

  const authSso = createAuthCommunicator();
  const responseSso = await authSso(ssotok!);

  if (!responseSso?.logged_in)
    return {
      error: true,
      data: null,
      token: ssotok!,
      code: 500,
      message: "Gagal login SSO",
    };

  return {
    code: 200,
    message: "success",
    error: false,
    data: responseSso.payload,
    token: ssotok!,
  };
}
