export type PaginateSuccessProps<T> = {
  page: number;
  per_page: number;
  total_item: number;
  total_pages: number;
  data: T;
};

export type SuccessProps<T> = {
  error: false;
  data: T;
  message: string;
};

type ErrorProps = {
  error: true;
  data: null;
  message: string;
};

type ApiResponse<T> = {
  error: boolean;
  code: number;
} & (ErrorProps | SuccessProps<T>);

export default ApiResponse;
