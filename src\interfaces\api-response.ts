export type PaginateSuccessProps<T> = {
  page: number;
  per_page: number;
  total_item: number;
  total_pages: number;
  content: T;
};

export type SuccessProps<T> = {
  error: false;
  content: T;
  message: string;
};

type ErrorProps = {
  error: true;
  content: null;
  message: string;
};

type ApiResponse<T> = {
  error: boolean;
  code: number;
} & (ErrorProps | SuccessProps<T>);

export default ApiResponse;
