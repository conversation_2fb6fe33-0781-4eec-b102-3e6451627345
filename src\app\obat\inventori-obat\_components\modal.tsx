"use client";

import { FormInput } from "@/components/custom/input";
import ApiResponse from "@/interfaces/api-response";
import {
  addToast,
  Button,
  Modal,
  ModalBody,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Skeleton,
  Tooltip,
  useDisclosure,
} from "@heroui/react";
import { useFormik } from "formik";
import { Eye, Pencil } from "lucide-react";
import { useEffect, useState } from "react";
import useSWR from "swr";
import { postObat } from "../schema/obatPost";
import { FilterDatePicker } from "@/components/custom/datepicker";
import { DateValue } from "@internationalized/date";

type payload = {
  bath: string;
  date: DateValue | null;
};

export default function Modals({ id }: { id: string }) {
  const [loading, setLoading] = useState(false);
  const { isOpen, onOpen, onOpenChange } = useDisclosure(); // state modals

  const initialValues = {
    bath: "",
    date: null,
  };

  const handleSubmit = async (value: payload) => {
    alert(JSON.stringify(value));
    // formik.setSubmitting(true);
    // try {
    //   const res = await patchtPoli({ name: value.name, id });
    //   if (res.error) {
    //     throw new Error(res?.message);
    //   }
    //   addToast({
    //     title: "Berhasil update data",
    //     color: "success",
    //   });
    //   window.location.reload();
    // } catch (error: any) {
    //   addToast({
    //     title: "Error",
    //     color: "danger",
    //     description: error.message ?? "Terjadi kesalahan",
    //   });
    // } finally {
    //   formik.setSubmitting(false);
    // }
  };

  const formik = useFormik<payload>({
    initialValues,
    enableReinitialize: true,
    validationSchema: postObat,
    onSubmit: handleSubmit,
  });

  // const getPoli = async () => {
  //   setLoading(true);
  //   try {
  //     const res = (await getPoliById({ id })) as any;
  //     formik.setFieldValue("name", res?.name);
  //     return res;
  //   } catch (error: any) {
  //     addToast({
  //       title: "Error",
  //       color: "danger",
  //       description: error.message ?? "Terjadi kesalahan",
  //     });
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  // useEffect(() => {
  //   if (isOpen) {
  //     getPoli();
  //   }
  // }, [id, isOpen]);

  return (
    <>
      <Tooltip content="Edit Data">
        <Button isIconOnly color="secondary" size="sm" onPress={onOpenChange}>
          <Eye className="w-4" />
        </Button>
      </Tooltip>
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} backdrop={"blur"}>
        <ModalContent>
          {(onClose) => (
            <>
              <form onSubmit={formik.handleSubmit}>
                <ModalHeader className="flex flex-col gap-1">
                  Tambah Batch Inventori
                </ModalHeader>
                <ModalBody className="mb-4">
                  <div className="flex flex-col gap-6">
                    <div className="flex flex-col gap-1">
                      <label htmlFor="poli" className="text-sm ">
                        {" "}
                        No. Batch
                      </label>
                      <FormInput
                        isClearable={false}
                        name={"bath"}
                        isNumeric={false}
                        formik={formik}
                        placeholder="Ketik No. Batch obat masuk"
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <label htmlFor="poli" className="text-sm ">
                        {" "}
                        Tanggal Perolehan
                      </label>
                      <FilterDatePicker name="date" formik={formik} />
                    </div>
                  </div>
                </ModalBody>
                <ModalFooter>
                  <Button
                    className="bg-default-50 border-[1px] border-md"
                    onPress={onClose}
                  >
                    Kembali
                  </Button>
                  <Button
                    color="primary"
                    type="submit"
                    isLoading={formik.isSubmitting}
                  >
                    Simpan
                  </Button>
                </ModalFooter>
              </form>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
