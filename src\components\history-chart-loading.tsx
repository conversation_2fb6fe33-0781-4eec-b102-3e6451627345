"use client";

import { Card, Skeleton } from "@heroui/react";

export default function LoadingChart() {
  return (
    <Card className="flex flex-row flex-wrap gap-4" radius="sm" shadow="none">
      <div className="flex-auto  space-y-5 p-4">
        <Skeleton className="rounded-lg">
          <div className="h-56 w-56 rounded-lg bg-default-300" />
        </Skeleton>
        <div className="space-y-3">
          <Skeleton className="w-3/5 rounded-lg">
            <div className="h-3 w-3/5 rounded-lg bg-default-200" />
          </Skeleton>
          <Skeleton className="w-4/5 rounded-lg">
            <div className="h-3 w-4/5 rounded-lg bg-default-200" />
          </Skeleton>
        </div>
      </div>

      <div className="flex-auto space-y-5 p-4">
        <Skeleton className="rounded-lg">
          <div className="h-56 w-56 rounded-lg bg-default-300" />
        </Skeleton>
        <div className="space-y-3">
          <Skeleton className="w-3/5 rounded-lg">
            <div className="h-3 w-3/5 rounded-lg bg-default-200" />
          </Skeleton>
          <Skeleton className="w-4/5 rounded-lg">
            <div className="h-3 w-4/5 rounded-lg bg-default-200" />
          </Skeleton>
        </div>
      </div>
      <div className="flex-auto space-y-5 p-4">
        <Skeleton className="rounded-lg">
          <div className="h-56 w-56 rounded-lg bg-default-300" />
        </Skeleton>
        <div className="space-y-3">
          <Skeleton className="w-3/5 rounded-lg">
            <div className="h-3 w-3/5 rounded-lg bg-default-200" />
          </Skeleton>
          <Skeleton className="w-4/5 rounded-lg">
            <div className="h-3 w-4/5 rounded-lg bg-default-200" />
          </Skeleton>
        </div>
      </div>
    </Card>
  );
}
