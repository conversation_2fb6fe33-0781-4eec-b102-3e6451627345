import { InputProps } from "@heroui/react";
import { KeyboardEvent } from "react";
import { Checkbox } from "@heroui/checkbox";
import { getIn } from "formik";

import { classNames } from "@/constant/constant";

type Props = {
  name?: string;
  formik: any;
  submitOnEnter?: boolean;
} & InputProps;

export function FilterCheckbox({
  name = "",
  formik,
  submitOnEnter,
  ...res
}: Props) {
  const isValid = !!(formik.touched[name] && formik.errors[name]);

  const handleKeyPress = (
    event: KeyboardEvent<HTMLInputElement> | KeyboardEvent
  ) => {
    if (event.key === "Enter") {
      event.preventDefault();
      formik.validateForm().then(() => formik.submitForm());
    }
  };

  return (
    <Checkbox
      classNames={classNames.input}
      color={isValid ? "danger" : "default"}
      isInvalid={isValid}
      {...formik.getFieldProps(name)}
      onKeyDown={submitOnEnter ? handleKeyPress : undefined}
      {...res}
    />
  );
}

type FormInputProps = {
  name?: string;
  formik: any;
  submitOnEnter?: boolean;
} & InputProps;

export function FormCheckbox({ name = "", formik, ...res }: FormInputProps) {
  const error = getIn(formik.errors, name);
  const touched = getIn(formik.touched, name);
  const value = getIn(formik.values, name) as string;

  const isValid = !!(touched && error);

  return (
    <Checkbox
      classNames={classNames.input}
      color={isValid ? "danger" : "default"}
      isInvalid={isValid}
      {...formik.getFieldProps(name)}
      isSelected={value}
      {...res}
    />
  );
}
