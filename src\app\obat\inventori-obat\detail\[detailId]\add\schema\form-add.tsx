import * as yup from "yup";

export const formAddDrug = yup.object({
  dateIn: yup.mixed().nullable().required("Tanggal masuk wajib diisi"),
  noBatch: yup.string().required("No. Batch wajib diisi"),
  TotalIn: yup.number().required("Total masuk wajib diisi"),
  leftOver: yup.number().required("Sisa wajib diisi"),
  dateExpired: yup.mixed().nullable().required("Tanggal expired wajib diisi"),
  company: yup.string().required("Perusahaan wajib diisi"),
});

export type formAddDrug = yup.InferType<typeof formAddDrug>;
