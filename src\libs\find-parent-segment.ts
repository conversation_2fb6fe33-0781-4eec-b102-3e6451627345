import { RouteItem } from "@/types/site-config";

export const findParentSegment = (
  items: RouteItem[],
  targetSegment: string[]
): string[][] | undefined => {
  const targetSegments = Array.isArray(targetSegment)
    ? targetSegment
    : [targetSegment];

  for (const item of items) {
    if (item.children) {
      // Check if any of the children match any of the target segments
      if (
        item.children.some((child) => {
          const childSegments = Array.isArray(child.segment)
            ? child.segment
            : [child.segment];

          return isActiveKey(childSegments, targetSegments);
        })
      ) {
        return item.segment;
      }
      // Recursively search in children
      const found = findParentSegment(item.children, targetSegments);

      if (found) {
        return found;
      }
    }
  }

  return undefined;
};

export const isActiveKey = (
  segments: string[][],
  targetSegment: string[]
): boolean => {
  return segments.some((segment) =>
    segment.every((seg) => targetSegment.includes(seg))
  );
};
