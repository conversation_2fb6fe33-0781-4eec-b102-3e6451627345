import { Suspense } from "react";
import FilterPoli from "./_components/filter";
import TableDokter from "./_components/table";
import { getDoctor } from "./actions";
import LoadingTable from "@/components/loadingTable";

export default async function PageDokter() {
  const res = await getDoctor();

  // Handle API error response
  if (res.error) {
    return (
      <div className="flex flex-col gap-4">
        <FilterPoli title="Poli Tugas" />
        <div className="text-center text-red-500">
          Error loading doctors: {res.message}
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      <FilterPoli title="Poli Tugas" />
      <div>
        <Suspense fallback={<LoadingTable />}>
          <TableDokter doctors={res} />
        </Suspense>
      </div>
    </div>
  );
}
