import { Suspense } from "react";
import FilterPoli from "./_components/filter";
import TableDokter from "./_components/table";
import { getDoctor } from "./actions";
import LoadingTable from "@/components/loadingTable";

export default async function PageDokter() {
  const res = await getDoctor();

  return (
    <div className="flex flex-col gap-4">
      <FilterPoli title="Poli Tugas" />
      <div>
        <Suspense fallback={<LoadingTable />}>
          <TableDokter doctors={res} />
        </Suspense>
      </div>
    </div>
  );
}
