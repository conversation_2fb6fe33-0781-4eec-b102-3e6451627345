"use client";

import { FilterAutocomplete } from "@/components/custom/autocomplete";
import { setSearchParams } from "@/libs/search-params";
import { AutocompleteItem } from "@heroui/react";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { getPoli } from "../actions";

type payloadSearch = {
  idUser: string;
};

export default function FilterPoli({ title }: { title: string }) {
  const majors = [
    { key: "1", value: "arsat" },
    { key: "2", value: "teguh" },
    { key: "3", value: "maulana" },
  ];

  const router = useRouter();

  const formik = useFormik<payloadSearch>({
    initialValues: {
      idUser: "",
    },
    onSubmit: (value) => {
      const filterParams = setSearchParams(value);
      router.replace(`?${filterParams.toString()}`);
    },
  });

  // const example = async () => {
  //   const res = await getPoli();
  //   console.log(res);
  // };
  // useEffect(() => {
  //   example();
  // }, []);

  return (
    <div className="bg-default-50 p-5 rounded-md">
      <div className="flex flex-col gap-6">
        <div className="flex flex-col gap-2">
          <h1 className="text-xl font-medium">Filter Data</h1>
          <p className="text-sm text-neutral-500">
            Digunakan untuk menyaring data spesifik yang ingin dicari
          </p>
        </div>
        <div className="flex flex-col gap-1">
          <label htmlFor="major_code" className="text-sm ">
            {title}
          </label>
          <FilterAutocomplete
            name="idUser"
            submitOnChange
            classNames={{ base: "bg-white dark:bg-inherit" }}
            formik={formik}
            placeholder={`ketik ${title}`}
            radius="sm"
            variant="bordered"
          >
            {majors.map((unit) => (
              <AutocompleteItem key={unit.key}>{unit.value}</AutocompleteItem>
            ))}
          </FilterAutocomplete>
        </div>
      </div>
    </div>
  );
}
