import * as yup from "yup";

// Schema untuk validasi item obat individual
const obatItemSchema = yup.object({
  id: yup.string().required("ID obat wajib diisi"),
  name: yup.string().required("Nama obat wajib diisi"),
  quantityIn: yup
    .number()
    .min(1, "Jumlah masuk minimal 1")
    .required("Jumlah masuk wajib diisi"),
  tglExpired: yup.mixed().nullable(), // DateValue bisa null
  perusahaan: yup.string().required("Nama perusahaan wajib diisi"),
});

export const postPenambahanObat = yup.object({
  tglMasuk: yup.mixed().nullable().required("Tanggal masuk wajib diisi"),
  noBatch: yup.string().required("No. Batch wajib diisi"),
  listObat: yup
    .array()
    .of(obatItemSchema)
    .min(1, "Minimal harus ada 1 obat")
    .required("Daftar obat wajib diisi"),
});

export type postPenambahanObat = yup.InferType<typeof postPenambahanObat>;
