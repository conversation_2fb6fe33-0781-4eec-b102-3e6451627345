"use client";

import { default as <PERSON><PERSON><PERSON><PERSON>, default as Button<PERSON>avi<PERSON> } from "@/components/custom/buttonNavigation";
import TablePoliklinik from "@/components/table/tablePoliklinik";
import { PATIENT_TYPE_LIST } from "@/constant/constant";
import { PropsTable } from "@/interfaces/tables";
import {
  Chip,
  Switch
} from "@heroui/react";
import { useCallback } from "react";

export const users = [
  {
    id: 1,
    name: "<PERSON>",
    mahasiswa: "gratis",
    pegawai: "10000",
    kel_pegawai: "20000",
    umum: "50000",
    pensiunan: "300000",
    status: "active",
    pasien: PATIENT_TYPE_LIST
  },
  {
    id: 2,
    name: "<PERSON>",
    mahasiswa: "gratis",
    pegawai: "10000",
    kel_pegawai: "20000",
    umum: "50000",
    pensiunan: "300000",
    status: "active",
    pasien: PATIENT_TYPE_LIST
  },
  {
    id: 3,
    name: "<PERSON>",
    mahas<PERSON>wa: "gratis",
    pegawai: "10000",
    kel_pegawai: "20000",
    umum: "50000",
    pensiunan: "300000",
    status: "active",
    pasien: PATIENT_TYPE_LIST
  },
];

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "name",
    label: "Nama Layanan",
  },
  {
    key: "mahasiswa",
    label: "Mahasiswa",
  },
  {
    key: "pegawai",
    label: "Pegawai",
  },
  {
    key: "kel_pegawai",
    label: "Kel-Pegawai",
  },
  {
    key: "umum",
    label: "Umum",
  },
  {
    key: "pensiunan",
    label: "Pensiunan",
  },
  {
    key: "status",
    label: "Status",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

export default function TableLayananPoliDetail({
  detailId,
}: {
  detailId: string;
}) {
  const result: any[] = users
  const pageInfo: any = {}


  const renderCell = useCallback(
    (item: any, column_key: any, index: number) => {
      switch (column_key) {
        case "no":
          return index + 1;
        case "name":
          return (
            <div className="flex flex-col gap-1">
              <p>{item.name}</p>
              <div className="flex gap-2">
                {item.pasien.map((e: any) => (
                <Chip radius="md" size="sm">{e.value}</Chip>
              ))}
              </div>
            </div>
          )
        case "status":
          return (
            <div className="flex gap-2 justify-start items-start ">
              <Switch
                size="sm"
                defaultSelected={item.status === "active"}
              />
              <p className="capitalize">{item.status}</p>
            </div>
          );
        case "action":
          return <ButtonNavigation href={`/referensi/layanan-poli/${detailId}/update/${item.id}`} tema="secondary" icon="pencil" tooltip="Update Data" isIcon={true} />

        default:
          return item[column_key];
      }
    },
    []
  );
  const props: PropsTable<any> = { columns, renderCell, data: result }
  return (
    <div className="bg-default-50 p-6 rounded-md flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <h1 className="font-medium text-xl">Daftar Layanan</h1>
        <ButtonAction href={`/referensi/layanan-poli/${detailId}/add-layanan`} tema="light" title="Layanan" />
      </div>
      <TablePoliklinik {...props} />
    </div>
  );
}
