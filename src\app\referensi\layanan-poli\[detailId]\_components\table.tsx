"use client";

import {
  default as <PERSON><PERSON>A<PERSON>,
  default as ButtonNavigation,
} from "@/components/custom/buttonNavigation";
import TablePoliklinik from "@/components/table/tablePoliklinik";
import { SmoothSwitch } from "@/components/ui/switch-smooth";
import { PropsTable } from "@/interfaces/tables";
import { addToast, Chip, Switch } from "@heroui/react";
import { use, useCallback, useState } from "react";
import { patchSwich } from "../../actions";

const transformData = (content: any) => {
  return content.map((item: any) => {
    // Membuat objek awal
    const transformedItem: Record<string, any> = {
      id: item.id,
      name: item.name,
      active: item.active,
    };

    // Menambahkan harga untuk setiap kategori
    item.tariffs.forEach((tariff: any) => {
      // Mengubah nama kategori menjadi UPPERCASE untuk konsistensi
      const categoryKey = tariff.category.toUpperCase();

      // Mengkonversi price ke string, jika 0 menjadi "gratis"
      transformedItem[categoryKey] =
        tariff.price === 0 ? "gratis" : tariff.price.toString();
    });

    return transformedItem;
  });
};

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "name",
    label: "Nama Layanan",
  },
  {
    key: "STUDENT",
    label: "Mahasiswa",
  },
  {
    key: "EMPLOYEE",
    label: "Pegawai",
  },
  {
    key: "FAMILY_OF_EMPLOYEE",
    label: "Kel-Pegawai",
  },
  {
    key: "PUBLIC",
    label: "Umum",
  },
  {
    key: "RETIRED",
    label: "Pensiunan",
  },
  {
    key: "active",
    label: "Status",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

interface Item {
  id: string;
  value: boolean;
}

export default function TableLayananPoliDetail({
  detailId,
  res,
}: {
  detailId: string;
  res: any;
}) {
  const data: any = use(res);
  const result: any[] = data?.content;
  const pageInfo: any = data?.page;
  const [items, setItems] = useState<Item[]>([]);

  const upsertItem = useCallback((id: string, value: boolean) => {
    setItems((prevItems) => {
      const existingItemIndex = prevItems.findIndex((item) => item.id === id);

      if (existingItemIndex >= 0) {
        const newItems = [...prevItems];
        newItems[existingItemIndex] = { id, value };
        return newItems;
      }
      return [...prevItems, { id, value }];
    });
  }, []);

  const getItemById = useCallback(
    (id: string): boolean | null => {
      const item = items.find((item) => item.id === id);
      return item ? item.value : null;
    },
    [items]
  );

  const handleChangeSwitch = async ({
    id,
    currentSwitch,
  }: {
    id: string;
    currentSwitch: boolean;
  }) => {
    try {
      const res = await patchSwich({ active: currentSwitch, id: id });
      return res;
    } catch (error: any) {
      addToast({
        title: "Error",
        color: "danger",
        description: error.message ?? "Terjadi kesalahan",
      });
    }
  };

  const renderCell = useCallback(
    (item: any, column_key: any, index: number) => {
      const currentActive = getItemById(item.id) ?? item.active;
      switch (column_key) {
        case "no":
          return index + 1;
        case "name":
          return (
            <div className="flex flex-col gap-1">
              <p>{item?.name}</p>
              <div className="flex gap-2">
                {item?.tariffs?.map((e: any) => (
                  <Chip radius="md" size="sm">
                    {e.category}
                  </Chip>
                ))}
              </div>
            </div>
          );
        case "active":
          return (
            <div className="flex gap-2 justify-start items-start ">
              <SmoothSwitch
                id={item.id}
                isSelected={currentActive}
                onValueChange={(isSelected) => {
                  upsertItem(item.id, isSelected);
                  handleChangeSwitch({
                    id: item.id,
                    currentSwitch: isSelected,
                  });
                }}
              />
              <p className="capitalize">
                {currentActive ? "aktif" : "tidak aktif"}
              </p>
            </div>
          );
        case "action":
          return (
            <ButtonNavigation
              href={`/referensi/layanan-poli/${detailId}/update/${item.id}`}
              tema="secondary"
              icon="pencil"
              tooltip="Update Data"
              isIcon={true}
            />
          );

        default:
          return item[column_key];
      }
    },
    [getItemById, upsertItem]
  );
  const props: PropsTable<any> = {
    columns,
    renderCell,
    data: transformData(result),
    pageInfo,
    isRender: true,
  };
  return (
    <div className="bg-default-50 p-6 rounded-md flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <h1 className="font-medium text-xl">Daftar Layanan</h1>
        <ButtonAction
          href={`/referensi/layanan-poli/${detailId}/add-layanan`}
          tema="light"
          title="Layanan"
        />
      </div>
      <TablePoliklinik {...props} />
    </div>
  );
}
