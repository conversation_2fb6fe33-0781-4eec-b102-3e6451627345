"use client";

import {
  default as <PERSON><PERSON>A<PERSON>,
  default as ButtonNavigation,
} from "@/components/custom/buttonNavigation";
import TablePoliklinik from "@/components/table/tablePoliklinik";
import { PATIENT_TYPE_LIST } from "@/constant/constant";
import { PropsTable } from "@/interfaces/tables";
import { Chip, Switch } from "@heroui/react";
import { use, useCallback } from "react";

const transformData = (content: any) => {
  return content.map((item: any) => {
    // Membuat objek awal
    const transformedItem: Record<string, any> = {
      id: item.id,
      name: item.name,
      status: item.active ? "active" : "inactive",
      pasien: PATIENT_TYPE_LIST, // Asumsi ini adalah konstanta yang sudah didefinisikan
    };

    // Menambahkan harga untuk setiap kategori
    item.tariffs.forEach((tariff: any) => {
      // Mengubah nama kategori menjadi UPPERCASE untuk konsistensi
      const categoryKey = tariff.category.toUpperCase();

      // Mengkonversi price ke string, jika 0 menjadi "gratis"
      transformedItem[categoryKey] =
        tariff.price === 0 ? "gratis" : tariff.price.toString();
    });

    return transformedItem;
  });
};

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "name",
    label: "Nama Layanan",
  },
  {
    key: "STUDENT",
    label: "Mahasiswa",
  },
  {
    key: "EMPLOYEE",
    label: "Pegawai",
  },
  {
    key: "FAMILY_OF_EMPLOYEE",
    label: "Kel-Pegawai",
  },
  {
    key: "PUBLIC",
    label: "Umum",
  },
  {
    key: "RETIRED",
    label: "Pensiunan",
  },
  {
    key: "active",
    label: "Status",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

export default function TableLayananPoliDetail({
  detailId,
  res,
}: {
  detailId: string;
  res: any;
}) {
  const data: any = use(res);
  const result: any[] = data?.content;
  const pageInfo: any = data?.page;

  const renderCell = useCallback(
    (item: any, column_key: any, index: number) => {
      switch (column_key) {
        case "no":
          return index + 1;
        case "name":
          return (
            <div className="flex flex-col gap-1">
              <p>{item?.name}</p>
              <div className="flex gap-2">
                {item?.tariffs?.map((e: any) => (
                  <Chip radius="md" size="sm">
                    {e.category}
                  </Chip>
                ))}
              </div>
            </div>
          );
        case "active":
          return (
            <div className="flex gap-2 justify-start items-start ">
              <Switch size="sm" defaultSelected={item.active} />
              <p className="capitalize">
                {item.active ? "aktif" : "tidak aktif"}
              </p>
            </div>
          );
        case "action":
          return (
            <ButtonNavigation
              href={`/referensi/layanan-poli/${detailId}/update/${item.id}`}
              tema="secondary"
              icon="pencil"
              tooltip="Update Data"
              isIcon={true}
            />
          );

        default:
          return item[column_key];
      }
    },
    []
  );
  const props: PropsTable<any> = {
    columns,
    renderCell,
    data: transformData(result),
    pageInfo
  };
  return (
    <div className="bg-default-50 p-6 rounded-md flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <h1 className="font-medium text-xl">Daftar Layanan</h1>
        <ButtonAction
          href={`/referensi/layanan-poli/${detailId}/add-layanan`}
          tema="light"
          title="Layanan"
        />
      </div>
      <TablePoliklinik {...props} />
    </div>
  );
}
