import { twMerge } from "tailwind-merge";
import clsx, { ClassValue } from "clsx";
import { DateValue } from "@heroui/react";
import { Table } from "@tanstack/react-table";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function cs(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const range = (init: number, limit: number) => {
  const arr = [];

  for (let i = init; i <= limit; i++) {
    arr.push(i);
  }

  return arr;
};

export function flattenObject<T extends object>(
  obj: T,
  parentKey: string = "",
  result: Record<string, any> = {}
) {
  for (const [key, value] of Object.entries(obj)) {
    const newKey = parentKey ? `${parentKey}.${key}` : key;

    if (typeof value === "object" && value !== null && !Array.isArray(value)) {
      // Recursively flatten nested objects
      flattenObject(value, newKey, result);
    } else {
      // Assign value to the flattened key
      result[newKey] = value;
    }
  }

  return result;
}

export function unflattenObject(
  flattenedObj: Record<string, any>
): Record<string, any> {
  const result: Record<string, any> = {};

  for (const [key, value] of Object.entries(flattenedObj)) {
    const keys = key.split(".");
    let current = result;

    keys.forEach((part, index) => {
      if (index === keys.length - 1) {
        current[part] = value;
      } else {
        current[part] = current[part] || {};
        current = current[part];
      }
    });
  }

  return result;
}

export function convertToFormData(
  obj: any,
  parentKey = "",
  formData = new FormData()
) {
  if (Array.isArray(obj)) {
    obj.forEach((value, index) => {
      const fullKey = `${parentKey}[${index}]`;

      convertToFormData(value, fullKey, formData);
    });
  } else if (obj && typeof obj === "object" && !(obj instanceof File)) {
    Object.keys(obj).forEach((key) => {
      const fullKey = parentKey ? `${parentKey}.${key}` : key;

      convertToFormData(obj[key], fullKey, formData);
    });
  } else {
    formData.append(parentKey, obj == null ? "" : obj); // Handle null/undefined as empty string
  }

  return formData;
}

export function convertDateToString(date: DateValue, showTime = false) {
  if (showTime) return date.toString();

  return `${date.toString()}T00:00:00`;
}

export function applyFiltersToTable<T>(
  table: Table<any>,
  filterParams: Partial<T>
): void {
  Object.keys(filterParams).forEach((params) => {
    const column = table.getColumn(params);
    const currentValue = column?.getFilterValue();

    if (currentValue !== filterParams[params as keyof T]) {
      column?.setFilterValue(filterParams[params as keyof T]);
    }
  });
}
