"use client";
import { Skeleton } from "@heroui/skeleton";
import {
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Table,
} from "@heroui/table";

export default function LoadingTable() {
  return (
    <Table aria-label="Skeleton table">
      <TableHeader>
        <TableColumn>
          <Skeleton className="h-3 w-1/3 rounded-lg" />
        </TableColumn>
        <TableColumn>
          <Skeleton className="h-3 w-1/3 rounded-lg" />
        </TableColumn>
        <TableColumn>
          <Skeleton className="h-3 w-1/3 rounded-lg" />
        </TableColumn>
      </TableHeader>
      <TableBody>
        <TableRow key="1">
          <TableCell>
            <Skeleton className="h-3 w-4/5 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-3/5 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-3/5 rounded-lg" />
          </TableCell>
        </TableRow>
        <TableRow key="2">
          <TableCell>
            <Skeleton className="h-3 w-2/5 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-1/2 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-1/5 rounded-lg" />
          </TableCell>
        </TableRow>
        <TableRow key="3">
          <TableCell>
            <Skeleton className="h-3 w-2/3 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-3/4 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-3/5 rounded-lg" />
          </TableCell>
        </TableRow>
        <TableRow key="4">
          <TableCell>
            <Skeleton className="h-3 w-1/3 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-1/4 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-2/5 rounded-lg" />
          </TableCell>
        </TableRow>
        <TableRow key="5">
          <TableCell>
            <Skeleton className="h-3 w-2/5 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-1/2 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-1/5 rounded-lg" />
          </TableCell>
        </TableRow>
        <TableRow key="6">
          <TableCell>
            <Skeleton className="h-3 w-2/3 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-3/4 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-3/5 rounded-lg" />
          </TableCell>
        </TableRow>
        <TableRow key="7">
          <TableCell>
            <Skeleton className="h-3 w-1/3 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-1/4 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-2/5 rounded-lg" />
          </TableCell>
        </TableRow>
        <TableRow key="8">
          <TableCell>
            <Skeleton className="h-3 w-2/5 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-1/2 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-1/5 rounded-lg" />
          </TableCell>
        </TableRow>
        <TableRow key="9">
          <TableCell>
            <Skeleton className="h-3 w-2/3 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-3/4 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-3/5 rounded-lg" />
          </TableCell>
        </TableRow>
        <TableRow key="10">
          <TableCell>
            <Skeleton className="h-3 w-1/3 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-1/4 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-2/5 rounded-lg" />
          </TableCell>
        </TableRow>
        <TableRow key="11">
          <TableCell>
            <Skeleton className="h-3 w-2/5 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-1/2 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-1/5 rounded-lg" />
          </TableCell>
        </TableRow>
        <TableRow key="12">
          <TableCell>
            <Skeleton className="h-3 w-2/3 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-3/4 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-3/5 rounded-lg" />
          </TableCell>
        </TableRow>
        <TableRow key="13">
          <TableCell>
            <Skeleton className="h-3 w-1/3 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-1/4 rounded-lg" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-3 w-2/5 rounded-lg" />
          </TableCell>
        </TableRow>
      </TableBody>
    </Table>
  );
}
