import CompFormAddLayanan from "@/components/formAddLayanan";
import { ContainerDiv } from "@/components/ui/container";
import { getLayananPoliById } from "../../../actions";

export default async function PageUpdateLayanan({ params: { idUpdate } }: any) {
  const res = getLayananPoliById({ id: idUpdate });
  return (
    <ContainerDiv>
      <CompFormAddLayanan mode={"edit"} data={res} id={idUpdate} />
    </ContainerDiv>
  );
}
