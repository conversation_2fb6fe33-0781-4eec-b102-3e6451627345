"use client";

import { cn, Spinner, SpinnerProps } from "@heroui/react";

type Props = {
  loading?: boolean;
} & SpinnerProps;
export default function FullLoadingPage({ loading, ...props }: Props) {
  return (
    <div
      className={cn(
        "fixed inset-0 h-screen bg-default-600 bg-opacity-70 z-50 hidden",
        { flex: loading }
      )}
    >
      <div className="relative h-screen w-screen flex items-center justify-center">
        <Spinner size="lg" {...props} />
      </div>
    </div>
  );
}
