import fetcher from "@/libs/fetch-api";
import { ResponsePoli } from "../../poli/_schema/typePoli";

export async function getLab() {
  return fetcher<ResponsePoli>({
    path: `/reference/laboratory`,
    options: {
      next: {
        tags: ["poli-estetika-2"],
      },
    },
  });
}

export async function getLabById({ id }: { id: string }) {
  return fetcher<any>({
    path: `/reference/laboratory/${id}`,
    options: {
      next: {
        tags: ["poli-estetika-2"],
      },
    },
  });
}

export async function postLab({
  name,
  active,
}: {
  name?: string | null;
  active?: boolean | null;
}) {
  return fetcher<any[]>({
    path: `/reference/laboratory`,
    options: {
      method: "POST",
      body: JSON.stringify({ name, active }),
    },
  });
}

export async function patchtLab({
  name,
  active,
  id,
}: {
  name?: string | null;
  active?: boolean | null;
  id: string;
}) {
  return fetcher<any[]>({
    path: `/reference/laboratory/${id}`,
    options: {
      next: {
        tags: ["poli-estetika-2"],
      },
      method: "PATCH",
      body: JSON.stringify({ name, active }),
    },
  });
}
