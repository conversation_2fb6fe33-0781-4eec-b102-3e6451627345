# Start Dockerfile
ARG VERSION=alpine3.18
ARG DIR=app

# Install dependencies only when needed
FROM node:${VERSION} AS deps
ARG DIR
WORKDIR /${DIR}
COPY package.json ./
COPY package-lock.json ./
RUN npm install

# Rebuild the source code only when needed
FROM node:${VERSION} AS builder
ARG DIR
WORKDIR /${DIR}
COPY . .
COPY --from=deps /${DIR}/node_modules ./node_modules

ARG NEXT_PUBLIC_SERVER_URL
ENV NEXT_PUBLIC_SERVER_URL=$NEXT_PUBLIC_SERVER_URL
ARG NEXT_PUBLIC_SERVER_LOCAL_URL
ENV NEXT_PUBLIC_SERVER_LOCAL_URL=$NEXT_PUBLIC_SERVER_LOCAL_URL
ARG NEXT_PUBLIC_APPS_URL
ENV NEXT_PUBLIC_APPS_URL=$NEXT_PUBLIC_APPS_URL
ARG NEXT_PUBLIC_SSO_URL
ENV NEXT_PUBLIC_SSO_URL=$NEXT_PUBLIC_SSO_URL
ARG NEXT_PUBLIC_SATU_URL
ENV NEXT_PUBLIC_SATU_URL=$NEXT_PUBLIC_SATU_URL
RUN npm run build

# Production image, copy all the files and run next
FROM node:${VERSION} AS runner

ARG NEXT_PUBLIC_SERVER_URL
ENV NEXT_PUBLIC_SERVER_URL=$NEXT_PUBLIC_SERVER_URL
ARG NEXT_PUBLIC_SERVER_LOCAL_URL
ENV NEXT_PUBLIC_SERVER_LOCAL_URL=$NEXT_PUBLIC_SERVER_LOCAL_URL
ARG NEXT_PUBLIC_APPS_URL
ENV NEXT_PUBLIC_APPS_URL=$NEXT_PUBLIC_APPS_URL
ARG NEXT_PUBLIC_SSO_URL
ENV NEXT_PUBLIC_SSO_URL=$NEXT_PUBLIC_SSO_URL
ARG NEXT_PUBLIC_SATU_URL
ENV NEXT_PUBLIC_SATU_URL=$NEXT_PUBLIC_SATU_URL
ARG DIR
WORKDIR /${DIR}
COPY --from=builder /${DIR}/public ./public
COPY --from=builder /${DIR}/.next/standalone ./
COPY --from=builder /${DIR}/.next/static ./.next/static
COPY --from=builder /${DIR}/package.json ./package.json

# Create a non-root user and use it
RUN addgroup -g 1001 -S nodejs && adduser -S nextjs -u 1001
RUN mkdir -p /${DIR}/log && chown -R nextjs:nodejs /${DIR}/log
USER nextjs

EXPOSE 3000
ENV PORT 3000

# Uncomment the following line in case you want to disable telemetry.
# ENV NEXT_TELEMETRY_DISABLED=1

ENTRYPOINT ["node", "server.js"]
