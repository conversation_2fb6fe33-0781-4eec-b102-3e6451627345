import { getSsotok } from "./get-ssotok";
import { logToApi } from "./log-to-api";

import ApiResponse from "@/interfaces/api-response";

type FetchProps = {
  path: string;
  baseURL?: string;
  options?: RequestInit;
};

export default async function fetcher<T>({
  path = "/",
  baseURL = process.env.NEXT_PUBLIC_SERVER_LOCAL_URL,
  options,
}: FetchProps): Promise<ApiResponse<T>> {
  const ssotok = await getSsotok();

  const finalOptions: RequestInit = {
    method: "GET",
    cache: "no-store",
    headers: {
      ...(options?.body instanceof FormData
        ? {}
        : { "Content-Type": "application/json" }),
      ...(ssotok && { Authorization: `Bearer ${ssotok}` }),
    },
    ...options,
  };

  const url = `${baseURL}${path}`;

  try {
    const response = await fetch(url, finalOptions);
    const status = response.status;
    const method = finalOptions.method || "GET";
    const contentType = response.headers.get("Content-Type");

    let responseBody;

    if (contentType && contentType.includes("application/json")) {
      responseBody = await response.json(); // Parse JSON response
    } else {
      responseBody = await response.text(); // Fallback to plain text
    }

    // Pengecekan Server Error Responses (5xx)
    if (status >= 500) {
      const errorMessage = `Server error: ${status} ${response.statusText} ${url}`;

      logToApi({ level: "error", message: errorMessage });
    }

    // Log the response
    logToApi({ level: "info", message: `${method} ${status}: ${url}` });

    // Log API success or error
    await logToApi({
      level: response.ok ? "info" : "error",
      message: response.ok
        ? `Successful ${method} ${path}`
        : `Failed ${method} ${path}`,
      meta: {
        url,
        method,
        status,
        data: response.ok ? null : JSON.stringify(responseBody),
        requestBody: JSON.stringify(finalOptions.body || null),
      },
    });

    // Handle API error response
    if (!response.ok || responseBody.error) {
      return {
        error: true,
        code: status,
        message: responseBody.message || response.statusText,
        data: responseBody?.data,
      };
    }

    // Return successful response
    return responseBody;
  } catch (error) {
    // Type assertion for `unknown`
    let errorMessage: string;

    if (error instanceof SyntaxError) {
      errorMessage = `There was a SyntaxError: ${error.message}`;
    } else if (error instanceof Error) {
      errorMessage = error.message;
    } else {
      errorMessage = String(error);
    }

    // Log the error to the API
    await logToApi({
      level: "error",
      message: "An unexpected error occurred",
      meta: {
        url,
        method: finalOptions.method || "GET",
        status: 500,
        data: errorMessage,
      },
    });

    return {
      error: true,
      code: 500,
      message: errorMessage,
      data: null,
    };
  }
}
