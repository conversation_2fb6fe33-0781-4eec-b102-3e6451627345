import LoadingTable from "@/components/loadingTable";
import Container from "@/components/ui/container";
import { Suspense } from "react";
import FilterPoli from "../poli/_components/filter";
import TableLayananPoli from "./_components/table";
import { getPoli } from "../poli/actions";

export default async function PageLayananPoli() {
  const resPoli = getPoli();
  return (
    <Container>
      <div className="flex flex-col gap-4">
        <FilterPoli title="Nama Obat" />
        <Suspense fallback={<LoadingTable />}>
          <TableLayananPoli res={resPoli} />
        </Suspense>
      </div>
    </Container>
  );
}
