import LoadingTable from "@/components/loadingTable";
import FilterPoli from "../poli/_components/filter";
import Table<PERSON>ayanan<PERSON>oli from "./_components/table";
import { Suspense } from "react";
import Container from "@/components/ui/container";

export default function PageLayananPoli() {
  return (
    <Container>
      <div className="flex flex-col gap-4">
        <FilterPoli title="Nama Obat" />
        <Suspense fallback={<LoadingTable />}>
          <TableLayananPoli />
        </Suspense>
      </div>
    </Container>
  );
}
