import { getSsotok } from "@/libs/get-ssotok";
import logger from "@/libs/logger";
import { createAuthCommunicator } from "@psi/sso-auth";
import { promises as fs } from "fs";
import { gunzipSync } from "zlib";

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const search = searchParams.get("file");

  const ssotok = await getSsotok();
  const auth = createAuthCommunicator();
  const user = await auth(ssotok!); // autentikasi user

  if (!user.logged_in)
    return new Response("Forbidden", { status: 403, statusText: "Forbidden" });

  logger.info(
    `Route API: - OK - Log accessed by [${user.payload.identity} - ${user.payload.name}]`
  );

  const filePath = process.cwd() + `/log/${search}`;

  if (search?.endsWith(".gz")) {
    const gzippedBuffer = await fs.readFile(filePath);
    const decompressedBuffer = gunzipSync(new Uint8Array(gzippedBuffer));
    const decompressedContent = decompressedBuffer.toString("utf8");

    return new Response(decompressedContent, {
      status: 200,
    });
  } else {
    const fileContent = await fs.readFile(filePath, "utf8");

    return new Response(fileContent, {
      status: 200,
    });
  }
}
