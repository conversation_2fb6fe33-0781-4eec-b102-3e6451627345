import { FormDatePicker } from "@/components/custom/datepicker";
import { FormInput, FormTextarea } from "@/components/custom/input";
import { FormSelect } from "@/components/custom/select";
import { SelectItem } from "@heroui/react";
import React from "react";

export const data = [
  {
    id: "1",
    nameObat: "Amoxilin",
    dosis: "500mg",
    bentuk: "tablet",
    satuan: "tablet",
    jenis: "analgenis",
  },
  {
    id: "2",
    nameObat: "Amoxilin",
    dosis: "100mg",
    bentuk: "tablet",
    satuan: "tablet",
    jenis: "analgenis",
  },
  {
    id: "3",
    nameObat: "Paracetamol",
    dosis: "1000mg",
    bentuk: "tablet",
    satuan: "tablet",
    jenis: "analgenis",
  },
];

export default function FormHeader({ formik }: { formik: any }) {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col gap-1 w-full">
        <label htmlFor="tglMasuk" className="text-sm">
          Judul
        </label>
        <FormTextarea
          name="title"
          formik={formik}
          classNames={{
            base: "w-full",
            input: "resize-y min-h-[40px]",
          }}
          placeholder="Enter your description"
          variant="bordered"
        />
      </div>
      <div className="grid lg:grid-cols-3  grid-cols-1 gap-4">
        <div className="flex flex-col gap-1 ">
          <label className="text-sm">Jenis</label>
          <FormSelect
            showSeachbar={true}
            name={`typeDrug`}
            formik={formik}
            placeholder="Masukkan nama obat"
          >
            {data.map((item) => (
              <SelectItem
                key={item.id}
                textValue={item.nameObat}
                // Menyimpan seluruh data item sebagai prop
              >
                <p>{item.nameObat}</p>
              </SelectItem>
            ))}
          </FormSelect>
        </div>
        <div className="flex flex-col gap-1 ">
          <label className="text-sm">Tipe</label>
          <FormSelect
            showSeachbar={true}
            name={`type`}
            formik={formik}
            placeholder="Masukkan nama obat"
          >
            {data.map((item) => (
              <SelectItem
                key={item.id}
                textValue={item.nameObat}
                // Menyimpan seluruh data item sebagai prop
              >
                <p>{item.nameObat}</p>
              </SelectItem>
            ))}
          </FormSelect>
        </div>
        <div className="flex flex-col gap-1 ">
          <label className="text-sm">Sumber Dana</label>
          <FormSelect
            showSeachbar={true}
            name={`fund`}
            formik={formik}
            placeholder="Masukkan nama obat"
          >
            {data.map((item) => (
              <SelectItem
                key={item.id}
                textValue={item.nameObat}
                // Menyimpan seluruh data item sebagai prop
              >
                <p>{item.nameObat}</p>
              </SelectItem>
            ))}
          </FormSelect>
        </div>
      </div>

      <div className="flex justify-between gap-4">
        <div className="flex flex-col gap-1 w-full">
          <label htmlFor="tglMasuk" className="text-sm">
            Nomor Belanja (Kontrak/Kuitansi)
          </label>
          <FormInput
            isClearable={false}
            name={`receipt`}
            formik={formik}
            placeholder="Ketik nomor belanja"
          />
        </div>
        <div className="flex flex-col gap-1 w-full">
          <label className="text-sm">Tanggal</label>
          <FormDatePicker name={`date`} formik={formik} />
        </div>
      </div>
    </div>
  );
}
