import { Permission } from "@/libs/access-control";
import { JSX } from "react";

export type RouteItem = {
  key: string;
  title: string;
  path: string;
  children?: RouteItem[];
  parent?: string;
  noMenu?: boolean;
  icon?: JSX.Element;
  access?: Permission[];
  segment: string[][];
  type?: "subMenu" | "divider";
};

export type BreadcrumbProps = {
  label: string | string[];
  segment: string | string[];
};

export type SiteConfig = {
  name: string;
  description: string;
  pages: RouteItem[];
  mobilePages: RouteItem[];
  breadcrumbs: BreadcrumbProps[];
};
