import { Permission } from "@/libs/access-control";

export interface RoleList {
  id: string;
  role_id: string;
  unit_id: string;
  active: boolean;
  status: boolean;
  unit_name: string;
  role_name: string;
  role: {
    id: string;
    access_menu: string[];
    role_id: string;
    unit_id: string;
    active: boolean;
    status: string;
    unit_name: string;
    role_name: string;
  };
}

export interface RoleUnit {
  role: Role;
  access_unit: string[];
  access_unit_list: AccessUnitList[];
}

export interface AccessUnitList {
  unit_id: string;
  unit_name: string;
  unit_init: string | string;
  type: string;
}

export interface Role {
  id: string;
  role_name: string;
  status: string;
  access_menu: Permission[];
  created_by: string;
  created_at: string;
  updated_by: string;
  updated_at: string;
  able_to_deleted: boolean;
  visible: boolean;
}

export interface RawMenu {
  key_menu: string;
  label_menu: string;
}

export interface RoleRequest {
  role_name: string;
  access_menu: string[];
}

export interface RoleBodyRequest {
  role_id: string;
  access_unit: string[];
  active: boolean;
}

export interface UserRequest {
  user_id: string;
  roles: RoleBodyRequest[];
}
