"use client";

import { Tab, Tabs } from "@heroui/react";
import { FolderClock, NotebookPen } from "lucide-react";
import HeaderInfo from "./_components/headerInfo";
import TablePencatatan from "./_components/tablePencatatan";
import SubPageRiwayat from "./subPage/riwayat";
import Container from "@/components/ui/container";

const tabs = [
  {
    id: "1",
    label: "Pencatatan Obat",
    icon: <NotebookPen className="w-5" />,
    content: <TablePencatatan />,
  },
  {
    id: "2",
    label: "Riwayat",
    icon: <FolderClock className="w-5" />,
    content: <SubPageRiwayat />,
  },
];

export default function PageDetailId() {
  return (
    <Container>
      <div className="  flex flex-col gap-4 lg:gap-6">
        <div className="bg-default-50 rounded-md">
          <HeaderInfo />
        </div>

        <div className="flex w-full flex-col bg-default-50 p-4 lg:p-6 rounded-md">
          <Tabs
            aria-label="Options"
            classNames={{
              tabList:
                "gap-6 w-full relative rounded-none p-0 border-b border-divider",
              cursor: "w-full bg-primary",
              tab: "max-w-fit px-0 h-12",
              tabContent:
                "group-data-[selected=true]:text-primary font-semibold",
            }}
            color="primary"
            variant="underlined"
          >
            {tabs.map((e) => (
              <Tab
                key={e.id}
                title={
                  <div className="flex items-center space-x-2">
                    {e.icon}
                    <span>{e.label}</span>
                  </div>
                }
              >
                {e.content}
              </Tab>
            ))}
          </Tabs>
        </div>
      </div>
    </Container>
  );
}
