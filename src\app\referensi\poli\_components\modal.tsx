"use client";

import { FormInput } from "@/components/custom/input";
import {
  addToast,
  <PERSON><PERSON>,
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  useDisclosure,
} from "@heroui/react";
import { useFormik } from "formik";
import { Pencil } from "lucide-react";
import { poliPost } from "../_schema/postPoli";
import { patchtPoli } from "../actions";

type payload = {
  name: string;
};

export default function Modals({
  id,
  active,
  name,
}: {
  id: string;
  active: boolean;
  name: string;
}) {
  const { isOpen, onOpen, onOpenChange } = useDisclosure(); // state modals

  const handleSubmit = async (value: { name: string }) => {
    formik.setSubmitting(true);
    try {
      const res = await patchtPoli({ name: value.name, id, active });
      if (res.error) {
        throw new Error(res?.message);
      }
      addToast({
        title: "Berhasil update data",
        color: "success",
      });
      window.location.reload();
    } catch (error: any) {
      addToast({
        title: "Error",
        color: "danger",
        description: error.message ?? "<PERSON><PERSON><PERSON><PERSON> k<PERSON>",
      });
    } finally {
      formik.setSubmitting(false);
    }
  };

  const formik = useFormik<payload>({
    initialValues: { name },
    validationSchema: poliPost,
    onSubmit: handleSubmit,
  });

  return (
    <>
      <Tooltip content="Edit Data">
        <Button isIconOnly color="secondary" size="sm" onPress={onOpenChange}>
          <Pencil className="w-4" />
        </Button>
      </Tooltip>
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} backdrop={"blur"}>
        <ModalContent>
          {(onClose) => (
            <>
              <form onSubmit={formik.handleSubmit}>
                <ModalHeader className="flex flex-col gap-1">
                  Tambah Poli
                </ModalHeader>
                <ModalBody className="mb-4">
                  <div className="flex flex-col gap-1">
                    <label htmlFor="name" className="text-sm ">
                      {" "}
                      Nama Poli
                    </label>
                    <FormInput
                      debounceMs={0}
                      name={"name"}
                      isNumeric={false}
                      formik={formik}
                      placeholder="Ketik nama poli"
                    />
                  </div>
                </ModalBody>
                <ModalFooter>
                  <Button
                    className="bg-default-50 border-[1px] border-md"
                    onPress={onClose}
                  >
                    Kembali
                  </Button>
                  <Button
                    color="primary"
                    type="submit"
                    isLoading={formik.isSubmitting}
                  >
                    Simpan
                  </Button>
                </ModalFooter>
              </form>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
