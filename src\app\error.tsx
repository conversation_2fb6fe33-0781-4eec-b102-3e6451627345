"use client";

import { useEffect } from "react";

import ErrorPage from "@/components/error-page";

export default function Error({
  error,
  reset,
}: {
  error: Error;
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    /* eslint-disable no-console */
    console.error(error);
  }, [error]);

  return <ErrorPage title="Something went wrong!" onClick={() => reset()} />;
}
