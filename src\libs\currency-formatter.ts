export function currencyFormatter(
  nominal: number,
  thousandSeparator = ".",
  showDecimal = true,
  decimalSeparator = ",",
) {
  if (typeof nominal !== "number") {
    throw new Error("Nominal harus berupa angka.");
  }

  // Konversi nominal ke string dengan format dua desimal
  const [integerPart, decimalPart] = nominal.toFixed(2).split(".");

  // Format bagian ribuan
  const formattedInteger = integerPart.replace(
    /\B(?=(\d{3})+(?!\d))/g,
    thousandSeparator,
  );

  // Gabungkan bagian ribuan dan desimal
  const formattedCurrency = `${formattedInteger}${decimalSeparator}${showDecimal ? decimalPart : ""}`;

  return formattedCurrency;
}
