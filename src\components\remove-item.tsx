"use client";

import React, { <PERSON>actN<PERSON>, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  useDisclosure,
  Button,
  addToast,
} from "@heroui/react";
import { Trash2 } from "lucide-react";
import { Tooltip } from "@heroui/tooltip";

import ApiResponse from "@/interfaces/api-response";

type props = {
  previewText?: string;
  title?: string;
  onOk: () => Promise<ApiResponse<unknown>>;
  children?: ReactNode;
};

export default function RemoveItem({
  onOk,
  previewText,

  title,
  children,
}: props) {
  const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure();
  const [loading, setLoading] = useState(false);

  const onSubmit = async () => {
    if (!onOk) return;

    setLoading(true);

    const response = await onOk();

    addToast({
      color: response.error ? "danger" : "success",
      title: `${response.error ? "Gagal" : "Berhasil"} Menghapus ${previewText ?? "Data"}`,
      description: response.error ? String(response?.data) : response?.message,
    });

    onClose();
    setLoading(false);
  };

  return (
    <>
      <Tooltip color="danger" content="Hapus Data">
        {children ? (
          React.cloneElement(children as React.ReactElement<any>, {
            onPress: onOpen,
          })
        ) : (
          <Button isIconOnly color="danger" size="sm" onPress={onOpen}>
            <Trash2 className="h-4" />
          </Button>
        )}
      </Tooltip>
      <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                Hapus {title ?? "Data"}
              </ModalHeader>
              <ModalBody>
                <div className="grid w-full grid-row-12 flex-wrap gap-4">
                  <div className="flex items-center space-x-2">
                    Apakah anda akan menghapus {previewText ?? "data"}?
                  </div>
                </div>
              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="light" onPress={onClose}>
                  Batal
                </Button>
                <Button
                  color="danger"
                  isLoading={loading}
                  type="submit"
                  onPress={onSubmit}
                >
                  Hapus
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
