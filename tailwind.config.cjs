import { heroui } from "@heroui/theme";

const colorTokens = require("./color-tokens.json");

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{ts,tsx}",
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ["var(--font-sans)"],
        mono: ["var(--font-mono)"],
        poppins: ["var(--font-poppins)", "sans-serif"],
      },
      backgroundColor: colorTokens,
      colors: colorTokens,
      backgroundImage: {
        "header-gradient": "linear-gradient(90deg, #E5FDC6 0%, #BEDCE2 100%)",
      },
    },
  },
  darkMode: "class",
  plugins: [
    heroui({
      addCommonColors: true,
      layout: {
        radius: {
          large: "12px",
          medium: "8px",
          small: "6px",
        },
        borderWidth: {
          large: "1px",
          medium: "1px",
          small: "1px",
        },
      },
      themes: {
        light: {
          colors: {
            background: "#F5F5F5", // or DEFAULT
            header: "#070D17",
            primary: {
              DEFAULT: colorTokens.semantic.primary[500],
              foreground: colorTokens.semantic.primary[25],
              50: colorTokens.semantic.primary[50],
              100: colorTokens.semantic.primary[100],
              200: colorTokens.semantic.primary[200],
              300: colorTokens.semantic.primary[300],
              400: colorTokens.semantic.primary[400],
              500: colorTokens.semantic.primary[500],
              600: colorTokens.semantic.primary[600],
              700: colorTokens.semantic.primary[700],
              800: colorTokens.semantic.primary[800],
              900: colorTokens.semantic.primary[900],
            },
            secondary: {
              DEFAULT: colorTokens.semantic.info[500],
              foreground: colorTokens.neutral[100],
            },
            danger: {
              DEFAULT: colorTokens.semantic.error[500],
              foreground: colorTokens.neutral[100],
              100: colorTokens.semantic.error[100],
              200: colorTokens.semantic.error[200],
              300: colorTokens.semantic.error[300],
              400: colorTokens.semantic.error[400],
              500: colorTokens.semantic.error[500],
              600: colorTokens.semantic.error[600],
              700: colorTokens.semantic.error[700],
              800: colorTokens.semantic.error[800],
              900: colorTokens.semantic.error[900],
            },
            warning: {
              DEFAULT: colorTokens.semantic.warning[500],
              foreground: colorTokens.neutral[100],
              100: colorTokens.semantic.warning[100],
              200: colorTokens.semantic.warning[200],
              300: colorTokens.semantic.warning[300],
              400: colorTokens.semantic.warning[400],
              500: colorTokens.semantic.warning[500],
              600: colorTokens.semantic.warning[600],
              700: colorTokens.semantic.warning[700],
              800: colorTokens.semantic.warning[800],
              900: colorTokens.semantic.warning[900],
            },
            success: {
              foreground: colorTokens.neutral[100],
            },
            default: {
              50: colorTokens.neutral[50],
              100: colorTokens.neutral[100],
              200: colorTokens.neutral[200],
              300: colorTokens.neutral[300],
              400: colorTokens.neutral[400],
              500: colorTokens.neutral[500],
              600: colorTokens.neutral[600],
              700: colorTokens.neutral[700],
              800: colorTokens.neutral[800],
              900: colorTokens.neutral[900],
            },
          },
        },
        dark: {
          colors: {
            // background: colorTokens.neutral[800],
            primary: {
              DEFAULT: colorTokens.semantic.primary[500],
              foreground: colorTokens.semantic.primary[25],
              50: colorTokens.semantic.primary[900],
              100: colorTokens.semantic.primary[800],
              200: colorTokens.semantic.primary[700],
              300: colorTokens.semantic.primary[600],
              400: colorTokens.semantic.primary[500],
              500: colorTokens.semantic.primary[400],
              600: colorTokens.semantic.primary[300],
              700: colorTokens.semantic.primary[200],
              800: colorTokens.semantic.primary[100],
              900: colorTokens.semantic.primary[50],
            },
          },
          // ... rest of the colors
        },
      },
    }),
  ],
};
