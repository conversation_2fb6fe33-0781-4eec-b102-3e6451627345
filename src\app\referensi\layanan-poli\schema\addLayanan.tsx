import * as yup from "yup";

export const layananAddSchema = yup.object({
  laboratorium: yup.string().required("Nama layanan wajib diisi"),
  pasienPengguna: yup
    .array()
    .min(1, "Pilih minimal satu jenis pasien")
    .required("Jenis pasien wajib dipilih"),
  mahasiswa: yup.number().when("pasienPengguna", {
    is: (values: string[]) => values?.includes("mahasiswa"),
    then: (schema) =>
      schema
        .required("Tarif untuk mahasiswa wajib diisi")
        .min(0, "Tarif tidak boleh negatif"),
    otherwise: (schema) => schema.notRequired().nullable(),
  }),
  pegawai: yup.number().when("pasienPengguna", {
    is: (values: string[]) => values?.includes("pegawai"),
    then: (schema) =>
      schema
        .required("Tarif untuk pegawai wajib diisi")
        .min(0, "Tarif tidak boleh negatif"),
    otherwise: (schema) => schema.notRequired(),
  }),
  "keluarga-pegawai": yup.number().when("pasienPengguna", {
    is: (values: string[]) => values?.includes("keluarga-pegawai"),
    then: (schema) =>
      schema
        .required("Tarif untuk pegawai wajib diisi")
        .min(0, "Tarif tidak boleh negatif"),
    otherwise: (schema) => schema.notRequired(),
  }),
  umum: yup.number().when("pasienPengguna", {
    is: (values: string[]) => values?.includes("umum"),
    then: (schema) =>
      schema
        .required("Tarif untuk pegawai wajib diisi")
        .min(0, "Tarif tidak boleh negatif"),
    otherwise: (schema) => schema.notRequired(),
  }),
  alumni: yup.number().when("pasienPengguna", {
    is: (values: string[]) => values?.includes("alumni"),
    then: (schema) =>
      schema
        .required("Tarif untuk pegawai wajib diisi")
        .min(0, "Tarif tidak boleh negatif"),
    otherwise: (schema) => schema.notRequired(),
  }),
  pensiunan: yup.number().when("pasienPengguna", {
    is: (values: string[]) => values?.includes("pensiunan"),
    then: (schema) =>
      schema
        .required("Tarif untuk pegawai wajib diisi")
        .min(0, "Tarif tidak boleh negatif"),
    otherwise: (schema) => schema.notRequired(),
  }),
});

export type LayananAddSchema = yup.InferType<typeof layananAddSchema>;
