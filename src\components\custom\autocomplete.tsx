import { Key, ReactNode } from "react";
import { FormikProps, getIn } from "formik";
import { Autocomplete, AutocompleteProps } from "@heroui/react";

import { classNames } from "@/constant/constant";
import { NestedKeyOf } from "@/libs/nested-key";

type FilterAutocompleteProps<T> = {
  name: keyof T;
  formik: FormikProps<T>;
  submitOnChange?: boolean;
  children?: any;
} & AutocompleteProps;

export function FilterAutocomplete<T>({
  name,
  formik,
  submitOnChange,
  children,
  ...res
}: FilterAutocompleteProps<T>) {
  const error = getIn(formik.errors, name);
  const touched = getIn(formik.touched, name);
  const value = getIn(formik.values, name) as string;

  const isInValid = touched && error;

  const handleKeyPress = (_e: Key | null) => {
    formik.validateForm().then(() => formik.submitForm());
  };

  return (
    <Autocomplete
      classNames={{ base: classNames.input.label }}
      color={isInValid ? "danger" : "default"}
      errorMessage={error?.toString()}
      isInvalid={isInValid}
      {...formik.getFieldProps(name)}
      selectedKey={value}
      onClear={() => {
        formik.setFieldValue(name, undefined);
      }}
      onSelectionChange={(e) => {
        formik.setFieldValue(name, e);
        if (submitOnChange) {
          handleKeyPress(e);
        }
      }}
      {...res}
    >
      {children}
    </Autocomplete>
  );
}

type FormAutocompleteProps<T> = {
  name: NestedKeyOf<T>;
  formik: FormikProps<T>;
  children?: ReactNode;
} & AutocompleteProps;

export function FormAutocomplete<T>({
  name,
  formik,
  children,
  onSelectionChange,
  ...res
}: FormAutocompleteProps<T>) {
  // const isValid = !!(
  //   formik.touched[name as keyof T] && formik.errors[name as keyof T]
  // );

  const error = getIn(formik.errors, name);
  const touched = getIn(formik.touched, name);
  const value = getIn(formik.values, name) as string;

  const isInValid = touched && error;

  return (
    <Autocomplete
      classNames={{ base: classNames.input.label }}
      color={isInValid ? "danger" : "default"}
      errorMessage={error?.toString()}
      isInvalid={isInValid}
      {...formik.getFieldProps(name)}
      selectedKey={value}
      onClear={() => {
        formik.setFieldValue(name, undefined);
      }}
      onSelectionChange={(e) => {
        if (onSelectionChange) onSelectionChange(e);
        formik.setFieldValue(name, e);
      }}
      {...res}
    >
      {children}
    </Autocomplete>
  );
}
