import React, { <PERSON><PERSON><PERSON>, ReactNode, SetStateAction } from "react";
import { clsx } from "clsx";
import { Button } from "@heroui/react";
import { DynamicIcon } from "lucide-react/dynamic";

const openClassNames = {
  right: "translate-x-0",
  left: "translate-x-0",
  top: "translate-y-0",
  bottom: "translate-y-0",
};

const closeClassNames = {
  right: "translate-x-full",
  left: "-translate-x-full",
  top: "-translate-y-full",
  bottom: "translate-y-full",
};

const backdropclassNames = {
  right: "inset-y-0 right-0",
  left: "inset-y-0 left-0",
  top: "inset-x-0 top-0",
  bottom: "inset-x-0 bottom-0",
};

type Props = {
  open?: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  side: "right" | "left" | "top" | "bottom";
  classNames?: {
    wrapper?: string;
    base?: string;
  };
  children?: ReactNode;
  header?: ReactNode;
};
const Drawer = ({
  open,
  setOpen,
  side = "right",
  classNames,
  children,
  header,
}: Props) => {
  return (
    <div
      aria-labelledby="slide-over"
      aria-modal="true"
      className="relative z-50"
      id={`dialog-${side}`}
      role="dialog"
      onClick={() => setOpen(!open)}
      onKeyUp={(e) => {
        if (e.key === "Esc") {
          setOpen(!open);
        }
      }}
    >
      <div
        className={clsx(
          "fixed inset-0 bg-default-300 bg-opacity-75 transition-all",
          {
            "opacity-100 duration-500 ease-in-out visible": open,
          },
          { "opacity-0 duration-500 ease-in-out invisible": !open },
          classNames?.base,
        )}
      />
      <div className={clsx({ "fixed inset-0 overflow-hidden": open })}>
        <div className="absolute inset-0 overflow-hidden">
          <div
            className={clsx(
              "pointer-events-none fixed max-w-full",
              backdropclassNames[side],
            )}
          >
            <div
              className={clsx(
                "pointer-events-auto relative w-full h-full transform transition ease-in-out duration-500",
                { [closeClassNames[side]]: !open },
                { [openClassNames[side]]: open },
              )}
              role="link"
              tabIndex={-1}
              onClick={(event) => {
                event.preventDefault();
                event.stopPropagation();
              }}
              onKeyUp={(event) => {
                event.preventDefault();
              }}
            >
              <div
                className={clsx(
                  "flex flex-col h-full overflow-y-auto p-3 md:p-6 shadow-xl bg-background w-max min-w-48 space-y-5",
                  {
                    "rounded-l-lg": side === "right",
                  },
                  {
                    "rounded-r-lg": side === "left",
                  },
                  classNames?.wrapper,
                )}
              >
                <div className="flex justify-between">
                  <div className="font-bold text-lg">{header}</div>
                  <Button
                    isIconOnly
                    size="sm"
                    variant="light"
                    onPress={() => setOpen(false)}
                  >
                    <DynamicIcon
                      className="h-4 text-default-foreground"
                      name="x"
                    />
                  </Button>
                </div>
                <div>{children}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Drawer;
