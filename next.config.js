/** @type {import('next').NextConfig} */
const nextConfig = {
  output: "standalone",
  transpilePackages: ["lucide-react", "rc-tree"], // add this
  images: {
    remotePatterns: [
      { hostname: "simsdm.usu.ac.id" },
      { hostname: "localhost" },
    ],
  },
  async rewrites() {
    return [
      {
        source: "/",
        destination: "/home",
      },
    ];
  },
  experimental: {
    serverActions: {
      allowedOrigins: [
        "dlc.usu.ac.id",
        "dev-dlc.usu.ac.id",
        "localhost:3000",
        "dlc-backend:8180",
      ],
    },
  },
};

module.exports = nextConfig;
