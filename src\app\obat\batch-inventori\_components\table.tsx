"use client";

import TablePoliklinik from "@/components/table/tablePoliklinik";
import { useCallback } from "react";
import { TypePoli } from "@/app/referensi/poli/_schema/typePoli";
import { PropsTable } from "@/interfaces/tables";
import Modals from "./modal";
import ModalPost from "./modalPost";
import ButtonNavigation from "@/components/custom/buttonNavigation";

const dummyApi = [
  {
    id: 1,
    noBatch: "123456789",
    tglPerolehan: "12/12/2021",
    jenisObat: "Obat A",
    sumObat: "10",
    almostExpired: "1",
    expired: "0",
  },
  {
    id: 2,
    noBatch: "123456789",
    tglPerolehan: "12/12/2021",
    jenisObat: "Obat A",
    sumObat: "10",
    almostExpired: "0",
    expired: "1",
  },
];

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "noBatch",
    label: "No. Batch",
  },
  {
    key: "tgl<PERSON><PERSON>lehan",
    label: "Tanggal Perolehan",
  },
  {
    key: "jenis<PERSON>bat",
    label: "Jenis Obat",
  },
  {
    key: "sumObat",
    label: "Jumlah Obat",
  },
  {
    key: "almostExpired",
    label: "Obat Hampir Expired",
  },
  {
    key: "expired",
    label: "Obat  Expired",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

type Props = {
  poli: any;
  onChangeSwitch?: (id: number) => void;
};

export default function TableObat({ poli }: Props) {
  const result: any[] = dummyApi;

  const renderCell = useCallback(
    (item: any, column_key: any, index: number) => {
      switch (column_key) {
        case "no":
          return index + 1;
        case "noBatch":
          return <p className="lg:w-[200px]  ">{item.noBatch}</p>;
        case "tglPerolehan":
          return <p className="  ">{item.tglPerolehan}</p>;
        case "almostExpired":
          return (
            <p
              className={`lg:!max-w-[40px] py-2 px-4 rounded font-medium ${
                item.almostExpired >= 1 ? "bg-yellow-200" : ""
              } `}
            >
              {item.almostExpired}
            </p>
          );
        case "expired":
          return (
            <p
              className={` lg:max-w-[40px] py-2 px-4 rounded font-medium ${
                item.expired >= 1 ? "bg-danger-200" : ""
              } `}
            >
              {item.almostExpired}
            </p>
          );
        case "action":
          return (
            <div className="flex-row-reverse flex items-center gap-2 justify-center">
              <Modals id={item.id} />
              <ButtonNavigation
                href={`/obat/batch-inventori/${item.id}`}
                tema="primary"
                icon="eye"
                tooltip="Lihat Detail"
                isIcon={true}
              />
            </div>
          );
        default:
          return item[column_key];
      }
    },
    []
  );
  const props: PropsTable<TypePoli> = {
    columns,
    renderCell,
    data: result,
    basePath: "/referensi/poli",
  };
  return (
    <div className="bg-default-50  rounded-md flex flex-col gap-6">
      <div className="flex justify-between items-center p-6 pb-0">
        <h1 className="font-medium text-xl">Daftar Bacth Inventori</h1>
        <ModalPost />
      </div>
      <div className="p-6">
        <TablePoliklinik {...props} />
      </div>
      <div className="p-6">
        <p className="font-medium mb-4">Keterangan</p>
        <div className="flex items-center gap-4 mb-2">
          <span className="block bg-danger-200 rounded h-5 w-5" />
          <p className="text-sm">Sudah melewati tanggal kadaluarsa</p>
        </div>
        <div className="flex items-center gap-4">
          <span className="block bg-yellow-200 rounded h-5 w-5" />
          <p className="text-sm">Hampir mendekati tanggal kadaluarsa</p>
        </div>
      </div>
    </div>
  );
}
