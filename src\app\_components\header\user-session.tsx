"use client";

import {
  Dropdown,
  DropdownTrigger,
  Avatar,
  DropdownMenu,
  DropdownItem,
  DropdownSection,
  User,
  addToast,
} from "@heroui/react";
import { useContext, useState } from "react";
import { LogOut } from "lucide-react";

import { UserContext } from "@/provider/user";
import { onChangeRole } from "@/app/action";
import signOut from "@/libs/sign-out";
import FullLoadingPage from "@/components/fullLoadingPage";

export default function UserSession() {
  const [loading, setLoading] = useState(false);
  const user = useContext(UserContext);

  const handleChangeRole = async (roleId: number) => {
    setLoading(true);
    const response = await onChangeRole(String(roleId));

    if (!response.error) {
      window.location.href = "/";

      return;
    }

    addToast({
      title: "Gagal Mengganti Role",
      description: response.message,
      color: "danger",
    });
  };

  return (
    <>
      <Dropdown className="max-w-6" placement="bottom-end">
        <DropdownTrigger>
          <div>
            <Avatar
              showFallback
              isBordered
              as="button"
              className="transition-transform flex sm:hidden "
              color="default"
              name={user?.name}
              size="sm"
              src={user?.user_unit.photo}
            />

            <User
              avatarProps={{
                src: user?.user_unit.photo,
              }}
              className="cursor-pointer hidden sm:flex max-w-52 bg-primary-50  dark:bg-default-200"
              classNames={{
                base: "bg-content3 rounded-full px-2",
                name: "text-default-700 line-clamp-1",
                wrapper: "w-full ",
              }}
              description={<span className="capitalize">SUPERADMIN</span>}
              name={user?.name}
              // name="Muhammad Isa Dadi Hasibuan"
            />
          </div>
        </DropdownTrigger>
        <DropdownMenu aria-label="Profile Actions" variant="flat">
          <DropdownSection showDivider>
            <DropdownItem
              key="profile"
              className="h-16 gap-2"
              textValue="profile"
            >
              <p className="text-xs">Signed in as:</p>
              <p className="font-semibold">{user?.name}</p>
              <p>{user?.identity}</p>
            </DropdownItem>
          </DropdownSection>

          <DropdownItem
            key="logout"
            color="danger"
            endContent={<LogOut className="text-default-600 h-4" />}
            textValue="logout"
            onPress={() => signOut()}
          >
            Log Out
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>

      <FullLoadingPage label="Mengganti Role..." loading={loading} />
    </>
  );
}
