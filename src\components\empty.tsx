"use client";
import { NotepadTextDashed } from "lucide-react";
import { ReactNode } from "react";

type Props = {
  title?: ReactNode;
};

export default function Empty({ title }: Props) {
  return (
    <div className="w-full inline-flex items-center justify-center flex-col gap-2 py-6">
      <NotepadTextDashed className="h-14 w-14 stroke-default-500" />
      {title || <p className="text-default-700">Tidak ada data</p>}
    </div>
  );
}
