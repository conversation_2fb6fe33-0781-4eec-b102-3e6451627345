import { Suspense } from "react";
import FilterPoli from "./_components/filter";
import TablePoli from "./_components/table";
import { getPoli } from "./actions";
import LoadingTable from "@/components/loadingTable";
import Container from "@/components/ui/container";

export default async function PagePoli() {
  const resPoli = getPoli();

  return (
    <Container>
      <div className="flex flex-col gap-4">
        <FilterPoli title="Poli" />
        <div>
          <Suspense fallback={<LoadingTable />}>
            <TablePoli poli={resPoli} />
          </Suspense>
        </div>
      </div>
    </Container>
  );
}
