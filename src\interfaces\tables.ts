export interface PageInfo {
  size: number;
  number: number;
  total_elements: number;
  total_pages: number;
}

export interface PropsTable<T> {
  data: T[];
  pageInfo?: PageInfo;
  columns: Array<{ key: string, label: string }>;
  renderCell: (item: T, columnKey: string, index: number) => React.ReactNode;
  loading?: boolean;
  basePath?: string;
  rowKey?: (item: T) => string | number;
  parameter?: boolean
  isTab?: boolean
}