export type PaginateSuccessProps<T> = {
  page: number;
  per_page: number;
  total_item: number;
  total_pages: number;
  data: T;
};

export type SuccessPoliProps<T> = {
  error: false;
  content: T;
  message: string;
};

type ErrorProps = {
  error: true;
  data: null;
  message: string;
};

export type PaginatePoli = {
  page: {
    size: number;
    number: number;
    total_elements: number;
    total_pages: number;
  };
};

type ApiPoliklinik<T> = {
  error: boolean;
  code: number;
} & (ErrorProps | SuccessPoliProps<T>);

export default ApiPoliklinik;
