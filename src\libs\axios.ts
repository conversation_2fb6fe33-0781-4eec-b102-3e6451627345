import axios from "axios";

import { logToApi } from "./log-to-api";

import ApiResponse from "@/interfaces/api-response";
import { getSsotok } from "@/libs/get-ssotok";

const headers: Record<string, any> = {};

const instance = axios.create({
  baseURL: `${process.env.NEXT_PUBLIC_SERVER_URL}/`,
  headers,
});

instance.interceptors.request.use(async (config) => {
  const ssotok = await getSsotok(); // Panggil hanya saat request dibuat

  if (ssotok) {
    config.headers["Authorization"] = `Bearer ${ssotok}`;
  }

  return config;
});

instance.interceptors.response.use(
  (response) => response, // Jika respons sukses, langsung return
  async (error) => {
    // Log error secara otomatis
    await logToApi({
      level: "error",
      message: "Axios client Error",
      meta: {
        message: error.message,
        url: error.config?.url,
        method: error.config?.method,
        status: error.response?.status,
        data: error.response?.data,
        requestBody: error.config?.data ? JSON.parse(error.config.data) : null,
      },
    });

    return Promise.reject(error); // Lanjutkan error ke caller
  }
);

export default instance;

export function handleAxiosError<T>(error: unknown): ApiResponse<T> {
  if (axios.isAxiosError<T>(error)) {
    return {
      code: error.status!,
      error: true,
      data: null,
      message: error.response?.data
        ? JSON.stringify(error.response.data)
        : error.message,
    };
  } else {
    return {
      code: 500,
      error: true,
      data: null,
      message: "Unexpected error occurred",
    };
  }
}
