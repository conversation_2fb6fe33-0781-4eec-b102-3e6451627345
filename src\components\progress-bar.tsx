"use client";

import { AppProgressBar } from "next-nprogress-bar";

import colorToken from "../../color-tokens.json";

const ProgressBar = ({ children }: { children: React.ReactNode }) => {
  return (
    <>
      {children}
      <AppProgressBar
        shallowRouting
        color={colorToken.semantic.primary[500]}
        height="3px"
        options={{ showSpinner: false }}
      />
    </>
  );
};

export default ProgressBar;
