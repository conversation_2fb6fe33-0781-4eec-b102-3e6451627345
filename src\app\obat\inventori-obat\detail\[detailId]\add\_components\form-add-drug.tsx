"use client";

import { data } from "@/app/obat/inventori-obat/add/_components/form-add";
import { FormDatePicker } from "@/components/custom/datepicker";
import { FormInput } from "@/components/custom/input";
import { FormSelect } from "@/components/custom/select";
import { Button, DateValue, SelectItem } from "@heroui/react";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";
import { formAddDrug } from "../schema/form-add";

type payload = {
  dateIn: DateValue | null;
  noBatch: string;
  TotalIn: number | null;
  leftOver: number | null;
  dateExpired: DateValue | null;
  company: string;
};

export type headerInfo = {
  name: string;
  dose: string;
  form: string;
  unit: string;
  type: string;
};

const initialHeaderInfo: headerInfo = {
  name: "Procol",
  dose: "500mG",
  form: "Botol",
  unit: "1",
  type: "500mG",
};

const initialValues: payload = {
  dateIn: null,
  noBatch: "",
  TotalIn: null,
  leftOver: null,
  dateExpired: null,
  company: "",
};

export default function FormAddDrug({
  mode = "add",
  initialData,
}: {
  mode?: "add" | "edit";
  initialData: any;
}) {
  const route = useRouter();

  const handleSubmit = async (value: payload) => {
    console.log({ value });
  };

  const formik = useFormik<payload>({
    initialValues: mode === "add" ? initialValues : initialData,
    enableReinitialize: true,
    validationSchema: formAddDrug,
    onSubmit: handleSubmit,
  });

  return (
    <div className="bg-default-50 rounded-md p-6 flex flex-col gap-6 w-full lg:w-[45%]  mx-auto">
      <h1 className="font-medium lg:text-xl pb-2">
        Form {mode === "add" ? "Tambah" : "Edit"} Obat Masuk
      </h1>
      <div className="grid grid-cols-2 lg:grid-cols-3">
        <div className="flex flex-col lg:col-span-1 gap-2 text-sm">
          <p>Nama Obat</p>
          <p>Dosis</p>
          <p>Bentuk</p>
          <p>Satuan</p>
          <p>Jenis</p>
        </div>
        <div className="flex flex-col gap-2 font-medium text-sm">
          <p>: {initialHeaderInfo.name}</p>
          <p>: {initialHeaderInfo.dose}</p>
          <p>: {initialHeaderInfo.form}</p>
          <p>: {initialHeaderInfo.unit}</p>
          <p>: {initialHeaderInfo.type}</p>
        </div>
      </div>{" "}
      <span className=" border-b-2 border-default-400 pb-2 " />
      <form action="" onSubmit={formik.handleSubmit}>
        <div className="flex flex-col gap-6">
          <div className="grid lg:grid-cols-2 gap-4 grid-cols-1">
            <div className="flex flex-col gap-1 md:col-span-2 lg:col-span-1">
              <label className="text-sm">Tanggal Masuk</label>
              <FormDatePicker name={`dateIn`} formik={formik} />
            </div>
            <div className="flex flex-col gap-1 md:col-span-2 lg:col-span-1">
              <label className="text-sm">No. Batch</label>
              <FormSelect
                showSeachbar={true}
                name={`noBatch`}
                formik={formik}
                placeholder="Pilih No batch"
              >
                {data.map((item) => (
                  <SelectItem
                    key={item.id}
                    textValue={item.nameObat}
                    // Menyimpan seluruh data item sebagai prop
                  >
                    <p>{item.nameObat}</p>
                  </SelectItem>
                ))}
              </FormSelect>
            </div>
          </div>

          <div className="grid lg:grid-cols-2 gap-4 grid-cols-1">
            <div className="flex flex-col gap-1 w-full">
              <label htmlFor="tglMasuk" className="text-sm">
                Jumlah Masuk
              </label>
              <FormInput
                name={`TotalIn`}
                isClearable={false}
                formik={formik}
                placeholder="Input jumlah"
              />
            </div>
            <div className="flex flex-col gap-1 w-full">
              <label htmlFor="tglMasuk" className="text-sm">
                Sisa
              </label>
              <FormInput
                name={`leftOver`}
                isClearable={false}
                formik={formik}
                placeholder="Sisa obat"
              />
            </div>
          </div>
          <div className="flex flex-col gap-1 md:col-span-2 lg:col-span-1">
            <label className="text-sm">Tanggal Expired</label>
            <FormDatePicker name={`dateExpired`} formik={formik} />
          </div>
          <div className="flex flex-col gap-1 w-full">
            <label htmlFor="tglMasuk" className="text-sm">
              Perusahaan / Produsen
            </label>
            <FormInput
              name={`company`}
              isClearable={false}
              formik={formik}
              placeholder="ketik nama"
            />
          </div>
        </div>
        <div className="flex gap-6 justify-end pt-4">
          <div className="flex justify-end pt-4">
            <Button
              color="default"
              className="bg-default-50 border-2 border-md"
              size="md"
              onPress={() => {
                formik.resetForm();
                route.back();
              }}
            >
              Kembali
            </Button>
          </div>
          <div className="flex justify-end pt-4">
            <Button
              type="submit"
              color="primary"
              size="md"
              isDisabled={formik.isSubmitting}
            >
              Simpan
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}
