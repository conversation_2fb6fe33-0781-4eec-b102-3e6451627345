# name: Staging Server

# on:
#   push:
#     branches: [ dev ]

# env:
#   REGISTRY: artifact.usu.ac.id
#   IMAGE_NAME: ${{ github.repository }}
#   USER_REGISTRY: usu-infra
#   WEB_HOOK: https://202.0.107.67:9443/api/webhooks/feb50f46-4894-4879-b0fa-4a4

# jobs:
#   build-and-push-image:
#     runs-on: ubuntu-latest
#     permissions:
#       contents: read
#       packages: write
#     steps:
#       - uses: actions/checkout@v2

#       - name: Log in to the Container registry
#         uses: docker/login-action@f054a8b539a109f9f41c372932f1ae047eff08c9
#         with:
#           registry: ${{ env.REGISTRY }}/v2
#           username: ${{ env.USER_REGISTRY }}
#           password: ${{ secrets.PASS_INFRA }}

#       - name: Extract metadata (tags, labels) for Docker
#         id: meta
#         uses: docker/metadata-action@98669ae865ea3cffbcbaa878cf57c20bbf1c6c38
#         with:
#           images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}

#       - name: Build and push Docker image
#         uses: docker/build-push-action@ad44023a93711e3deb337508980b4b5e9bcdc5dc
#         with:
#           context: .
#           push: true
#           tags: ${{ steps.meta.outputs.tags }}
#           labels: ${{ steps.meta.outputs.labels }}
#           build-args: |
#             NEXT_PUBLIC_SERVER_LOCAL_URL=https://staging-satu-mahasiswa.usu.ac.id/api
#             NEXT_PUBLIC_SERVER_URL=https://staging-satu-mahasiswa.usu.ac.id/api
#             NEXT_PUBLIC_APPS_URL=https://staging-satu.usu.ac.id/mahasiswa

#   deploy:
#     runs-on: ubuntu-latest
#     needs:
#       - build-and-push-image
#     steps:
#       - name: Trigger Portainer Webhook
#         run: |
#           curl --insecure -X POST ${{ env.WEB_HOOK }}
