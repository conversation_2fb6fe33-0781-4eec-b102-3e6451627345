"use client";

import ButtonNavigation from "@/components/custom/buttonNavigation";
import TablePoliklinik from "@/components/table/tablePoliklinik";
import { PropsTable } from "@/interfaces/tables";
import { use, useCallback } from "react";
import { ResponsePoli, TypePoli } from "../../poli/_schema/typePoli";

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "name",
    label: "Nama Poli",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

export default function TableLayananPoli({ res }: { res: any }) {
  const data: ResponsePoli = use(res);
  const result: TypePoli[] = data?.content ?? [];
  const pageInfo: any = data?.page;

  const renderCell = useCallback(
    (category: any, column_key: any, index: number) => {
      switch (column_key) {
        case "no":
          return index + 1;
        case "name":
          return <div className="">{category.name}</div>;
        case "action":
          return (
            <ButtonNavigation
              href={`/referensi/layanan-poli/${category.id}`}
              tema="secondary"
              icon="eye"
              tooltip="Lihat Detail"
              isIcon={true}
            />
          );

        default:
          return category[column_key];
      }
    },
    []
  );
  const props: PropsTable<any> = {
    columns,
    renderCell,
    data: result,
    pageInfo: pageInfo,
    basePath: "/referensi/layanan-poli",
  };
  return (
    <div className="bg-default-50 p-6 rounded-md flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <h1 className="font-medium text-xl">Daftar Layanan</h1>
      </div>
      <TablePoliklinik {...props} />
    </div>
  );
}
