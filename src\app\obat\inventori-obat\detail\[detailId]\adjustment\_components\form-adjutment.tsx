"use client";

import { FormDatePicker } from "@/components/custom/datepicker";
import { FormInput, FormTextarea, FormUpload } from "@/components/custom/input";
import { FormSelect } from "@/components/custom/select";
import { Button, DateValue, SelectItem } from "@heroui/react";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";

import { formAdjusment } from "../schema/form-adjustment";
import { data } from "@/app/obat/inventori-obat/add/_components/form-add";

type payload = {
  dateAdjustment: DateValue | null;
  noBatch: string;
  TotalReduced: number | null;
  reason: number | null;
  description: string | null;
  attachment: string | null;
};

export type headerInfo = {
  name: string;
  dose: string;
  form: string;
  unit: string;
  type: string;
};

const initialHeaderInfo: headerInfo = {
  name: "Procol",
  dose: "500mG",
  form: "Botol",
  unit: "1",
  type: "500mG",
};

const initialValues: payload = {
  dateAdjustment: null,
  noBatch: "",
  TotalReduced: null,
  reason: null,
  description: "",
  attachment: null,
};

export default function FormAdjustment({
  mode = "add",
  initialData,
}: {
  mode?: "add" | "edit";
  initialData: any;
}) {
  const route = useRouter();

  const handleSubmit = async (value: payload) => {
    console.log({ value });
  };

  const formik = useFormik<payload>({
    initialValues: initialValues,
    enableReinitialize: true,
    validationSchema: formAdjusment,
    onSubmit: handleSubmit,
  });

  return (
    <div className="bg-default-50 rounded-md p-6 flex flex-col gap-6 w-full lg:w-[45%]  mx-auto">
      <h1 className="font-medium lg:text-xl pb-2">
        Form Penyesuaian Jumlah Obat
      </h1>
      <div className="grid grid-cols-2 lg:grid-cols-3">
        <div className="flex flex-col lg:col-span-1 gap-2 text-sm">
          <p>Nama Obat</p>
          <p>Dosis</p>
          <p>Bentuk</p>
          <p>Satuan</p>
          <p>Jenis</p>
        </div>
        <div className="flex flex-col gap-2 font-medium text-sm">
          <p>: {initialHeaderInfo.name}</p>
          <p>: {initialHeaderInfo.dose}</p>
          <p>: {initialHeaderInfo.form}</p>
          <p>: {initialHeaderInfo.unit}</p>
          <p>: {initialHeaderInfo.type}</p>
        </div>
      </div>{" "}
      <span className=" border-b-2 border-default-400 pb-2 " />
      <form action="" onSubmit={formik.handleSubmit}>
        <div className="flex flex-col gap-6">
          <div className="grid lg:grid-cols-2 gap-4 grid-cols-1">
            <div className="flex flex-col gap-1 md:col-span-2 lg:col-span-1">
              <label className="text-sm">Tanggal Penyesuain</label>
              <FormDatePicker name={`dateAdjustment`} formik={formik} />
            </div>
            <div className="flex flex-col gap-1 w-full">
              <label htmlFor="tglMasuk" className="text-sm">
                No. Batch
              </label>
              <FormInput
                name={`noBatch`}
                isClearable={false}
                formik={formik}
                placeholder="Input jumlah"
              />
            </div>
          </div>

          <div className="grid lg:grid-cols-2 gap-4 grid-cols-1">
            <div className="flex flex-col gap-1 w-full">
              <label htmlFor="tglMasuk" className="text-sm">
                Jumlah Barang Dikurangi
              </label>
              <FormInput
                name={`TotalReduced`}
                isNumeric={true}
                isClearable={false}
                formik={formik}
                placeholder="Input jumlah"
              />
            </div>
            <div className="flex flex-col gap-1 md:col-span-2 lg:col-span-1">
              <label className="text-sm">Alasan Pengurangan</label>
              <FormSelect
                showSeachbar={true}
                name={`reason`}
                formik={formik}
                placeholder="Pilih Alasan"
              >
                {data.map((item) => (
                  <SelectItem
                    key={item.id}
                    textValue={item.nameObat}
                    // Menyimpan seluruh data item sebagai prop
                  >
                    <p>{item.nameObat}</p>
                  </SelectItem>
                ))}
              </FormSelect>
            </div>
          </div>
          <div className="flex flex-col gap-1 md:col-span-2 lg:col-span-1">
            <label className="text-sm">Keterangan Tambahan</label>
            <FormTextarea
              name={`description`}
              formik={formik}
              placeholder="ketikan petunjuk pemakaian"
            />
          </div>
          <div className="flex flex-col gap-1 w-full">
            <label htmlFor="tglMasuk" className="text-sm">
              Lampirkan foto obat dengan situasi dilaporkan
            </label>
            <FormUpload
              name={`attachment`}
              isClearable={false}
              formik={formik}
            />
          </div>
        </div>
        <div className="flex gap-6 justify-end pt-4">
          <div className="flex justify-end pt-4">
            <Button
              color="default"
              className="bg-default-50 border-2 border-md"
              size="md"
              onPress={() => {
                formik.resetForm();
                route.back();
              }}
            >
              Kembali
            </Button>
          </div>
          <div className="flex justify-end pt-4">
            <Button
              type="submit"
              color="primary"
              size="md"
              isDisabled={formik.isSubmitting}
            >
              Simpan
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}
