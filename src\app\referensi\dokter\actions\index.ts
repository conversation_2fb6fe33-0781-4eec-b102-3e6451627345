import { Doctor<PERSON><PERSON>R<PERSON>ponse } from "@/interfaces/refreence/doctor";
import fetcher from "@/libs/fetch-api";

export async function getDoctor() {
  return fetcher<DoctorApiResponse>({
    path: `/reference/doctor`,
    options: {
      next: {
        tags: ["reference/doctor"],
      },
    },
  });
}

export async function postDoctor({
  fullName,
  polyAssociations,
}: {
  fullName: string;
  polyAssociations: Array<{ id: string }>;
}) {
  return fetcher<DoctorApiResponse>({
    path: `/reference/doctor`,
    options: {
      method: "POST",
      body: JSON.stringify({
        full_name_with_degree: fullName,
        poly_associations: polyAssociations,
      }),
    },
  });
}
export async function patchDoctor({
  fullName,
  polyAssociations,
  id,
}: {
  fullName: string;
  polyAssociations: Array<{ id: string }>;
  id: string;
}) {
  return fetcher<DoctorApiResponse>({
    path: `/reference/doctor/${id}`,
    options: {
      method: "PATCH",
      body: JSON.stringify({
        full_name_with_degree: fullName,
        poly_associations: polyAssociations,
      }),
    },
  });
}
