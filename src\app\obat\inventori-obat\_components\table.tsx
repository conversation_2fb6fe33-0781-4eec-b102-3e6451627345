"use client";

import ButtonNavigation from "@/components/custom/buttonNavigation";
import TablePoliklinik from "@/components/table/tablePoliklinik";
import { PropsTable } from "@/interfaces/tables";
import { useCallback } from "react";

const dummyApi = [
  {
    id: 1,
    name: "<PERSON>bat A",
    bentuk: "Tablet",
    satuan: "Buah",
    jenis: "Obat Bebas",
    totalStok: "10",
    almostExpired: "1",
    expired: "0",
  },
  {
    id: 2,
    name: "<PERSON>bat B",
    bentuk: "Tablet",
    satuan: "Buah",
    jenis: "Obat Bebas",
    totalStok: "10",
    almostExpired: "0",
    expired: "1",
  },
];

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "name",
    label: "Nama Obat",
  },
  {
    key: "bentuk",
    label: "Bentuk",
  },
  {
    key: "satuan",
    label: "Satuan",
  },
  {
    key: "jenis",
    label: "<PERSON><PERSON>",
  },
  {
    key: "totalStok",
    label: "Total Stok",
  },
  {
    key: "almostExpired",
    label: "<PERSON>bat Hampi<PERSON> Expired",
  },
  {
    key: "expired",
    label: "Obat  Expired",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

type Props = {
  poli: any;
  onChangeSwitch?: (id: number) => void;
};

export default function TableObat({ poli }: Props) {
  const result: any[] = dummyApi;

  const handleChangeSwitch = async ({
    id,
    currentSwitch,
  }: {
    id: string;
    currentSwitch: boolean;
  }) => {
    // try {
    //   const res = await patchtPoli({ active: !currentSwitch, id: id });
    //   return res;
    // } catch (error: any) {
    //   addToast({
    //     title: "Error",
    //     color: "danger",
    //     description: error.message ?? "Terjadi kesalahan",
    //   });
    // }
  };

  const renderCell = useCallback(
    (item: any, column_key: any, index: number) => {
      switch (column_key) {
        case "no":
          return index + 1;
        case "name":
          return <p className="lg:w-[200px]  ">{item.name}</p>;
        case "bentuk":
          return <p className="lg:w-[150px]  ">{item.bentuk}</p>;
        case "satuan":
          return <p className="lg:w-[150px]  ">{item.satuan}</p>;
        case "jenis":
          return <p className="lg:w-[150px]  ">{item.jenis}</p>;
        case "totalStok":
          return <p className="lg:w-[150px]  ">{item.totalStok}</p>;

        case "almostExpired":
          return (
            <p
              className={`lg:w-[40px] py-2 px-4 rounded  ${
                item.almostExpired >= 1
                  ? "bg-yellow-200 font-bold text-yellow-600"
                  : ""
              } `}
            >
              {item.almostExpired}
            </p>
          );
        case "expired":
          return (
            <p
              className={` lg:w-[40px] py-2 px-4 rounded  ${
                item.expired >= 1
                  ? "bg-danger-200 font-bold  text-red-600"
                  : "text-black"
              } `}
            >
              {item.expired}
            </p>
          );
        case "action":
          return (
            <ButtonNavigation
              href={`/obat/inventori-obat/detail/${item.id}`}
              tema="secondary"
              icon="eye"
              tooltip="Lihat Detail"
              isIcon={true}
            />
          );
        default:
          return item[column_key];
      }
    },
    []
  );
  const props: PropsTable<any> = {
    columns,
    renderCell,
    data: result,
    basePath: "/obat/inventori-obat",
  };
  return (
    <div className="bg-default-50  rounded-md flex flex-col gap-6">
      <div className="flex justify-between items-center p-6 pb-0">
        <h1 className="font-medium text-xl">Daftar inventori Obat</h1>
        <ButtonNavigation
          href={`/obat/inventori-obat/add`}
          tema="light"
          icon="plus"
          tooltip="Tambah Data"
          title="Penambahan Masal"
        />
      </div>
      <div className="p-6">
        <TablePoliklinik {...props} />
      </div>
      <div className="p-6">
        <p className="font-medium mb-4">Keterangan</p>
        <div className="flex items-center gap-4 mb-2">
          <span className="block bg-danger-200 rounded h-5 w-5" />
          <p className="text-sm">Sudah melewati tanggal kadaluarsa</p>
        </div>
        <div className="flex items-center gap-4">
          <span className="block bg-yellow-200 rounded h-5 w-5" />
          <p className="text-sm">Hampir mendekati tanggal kadaluarsa</p>
        </div>
      </div>
    </div>
  );
}
