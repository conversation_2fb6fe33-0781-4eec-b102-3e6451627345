import { RouteItem, SiteConfig } from "@/types/site-config";
import {
  ClipboardPlus,
  Home,
  Pill,
  Settings,
  SlidersHorizontal,
  User,
  UserCog,
} from "lucide-react";

export const mainPages: RouteItem[] = [
  {
    key: "/home",
    path: "/home",
    title: "Home",
    icon: <Home />,
    access: ["*"],
    segment: [["home"]],
  },
  {
    key: "/registrasi",
    type: "divider",
    title: "REGISTRASI",
    path: "#",
    segment: [["registrasi"]],
  },
  {
    key: "/kunjungan",
    path: "/kunjungan",
    title: "Kunjungan",
    icon: <ClipboardPlus />,
    access: ["*"],
    segment: [["kunjungan"]],
  },
  {
    key: "/obat",
    path: "/obat",
    title: "Obat",
    icon: <Pill />,
    access: ["*"],
    segment: [["obat"]],
    type: "subMenu",
    children: [
      {
        key: "/obat/batch-inventori",
        icon: <Pill className="w-5" />,
        path: "/obat/batch-inventori",
        title: "Batch Inventori",
        parent: "/obat",
        access: ["role.*", "role.get"],
        segment: [["obat", "batch-inventori"]],
      },
      {
        key: "/obat/inventori-obat",
        icon: <Pill className="w-5" />,
        path: "/obat/inventori-obat",
        title: "Inventori Obat",
        parent: "/obat",
        access: ["role.*", "role.get"],
        segment: [["obat", "inventori-obat"]],
      },
    ],
  },
  {
    key: "/referensi",
    path: "/referensi",
    title: "Referensi",
    icon: <SlidersHorizontal className="w-5" />,
    access: ["*"],
    segment: [["referensi"]],
    type: "subMenu",
    children: [
      {
        key: "/referensi/poli",
        icon: <SlidersHorizontal className="w-5" />,
        path: "/referensi/poli",
        title: "Poli",
        parent: "/poli",
        access: ["role.*", "role.get"],
        segment: [["referensi", "poli"]],
      },
      {
        key: "/referensi/laboratorium",
        icon: <SlidersHorizontal className="w-5" />,
        path: "/referensi/laboratorium",
        title: "Laboratorium",
        parent: "/laboratorium",
        access: ["role.*", "role.get"],
        segment: [["referensi", "laboratorium"]],
      },
      {
        key: "/referensi/layanan-poli",
        icon: <SlidersHorizontal className="w-5" />,
        path: "/referensi/layanan-poli",
        title: "Layanan Poli",
        parent: "/layanan-poli",
        access: ["role.*", "role.get"],
        segment: [["referensi", "layanan-poli"]],
      },
      {
        key: "/referensi/dokter",
        icon: <SlidersHorizontal className="w-5" />,
        path: "/referensi/dokter",
        title: "Tenaga Medis",
        parent: "/dokter",
        access: ["role.*", "role.get"],
        segment: [["referensi", "dokter"]],
      },
      {
        key: "/referensi/pemeriksaan-lab",
        icon: <SlidersHorizontal className="w-5" />,
        path: "/referensi/pemeriksaan-lab",
        title: "Pemeriksaan Lab",
        parent: "/pemeriksaan-lab",
        access: ["role.*", "role.get"],
        segment: [["referensi", "pemeriksaan-lab"]],
      },
    ],
  },
  {
    key: "/setting",
    path: "/setting",
    title: "Pengguna",
    icon: <Settings />,
    segment: [["setting"]],
    type: "subMenu",
    children: [
      {
        key: "/setting/role",
        icon: <UserCog />,
        path: "/setting/role",
        title: "Hak Akses",
        parent: "/setting",
        access: ["role.*", "role.get"],
        segment: [["setting", "role"]],
      },
      {
        key: "/setting/user",
        path: "/setting/user",
        icon: <User />,
        title: "Pengguna",
        parent: "/setting",
        access: ["user.*", "user.get"],
        segment: [["setting", "user"]],
      },
    ],
  },
];

export const siteConfig: SiteConfig = {
  name: "POLIKLINIK USU",
  description: "Medical Record POLIKLINIK Universitas Sumatera Utara",
  pages: mainPages,
  mobilePages: mainPages,
  breadcrumbs: [
    {
      label: "Beranda",
      segment: ["admin", "home"],
    },
    {
      label: "Peran",
      segment: ["admin", "role"],
    },
    {
      label: "Pengguna",
      segment: ["admin", "user"],
    },

    {
      label: "Poliklinik",
      segment: ["referensi", "poli"],
    },
    {
      label: "Laboratorium",
      segment: ["referensi", "laboratorium"],
    },
    {
      label: "Layanan Poli",
      segment: ["referensi", "layanan-poli"],
    },
    {
      label: "Detail",
      segment: ["referensi", "layanan-poli", "detailId"],
    },
    {
      label: "Edit Layanan",
      segment: ["referensi", "layanan-poli", "detailId", "update"],
    },
    {
      label: "Tambah Layanan",
      segment: ["referensi", "layanan-poli", "detailId", "add-layanan"],
    },
    {
      label: "Tenaga Medis",
      segment: ["referensi", "dokter"],
    },
    {
      label: "Pemeriksaan Lab",
      segment: ["referensi", "pemeriksaan-lab"],
    },
    {
      label: "Detail",
      segment: ["referensi", "pemeriksaan-lab", "detailId"],
    },
    {
      label: "Tambah Layanan",
      segment: ["referensi", "pemeriksaan-lab", "tambah-layanan"],
    },
    {
      label: "Edit Layanan",
      segment: ["referensi", "pemeriksaan-lab", "detailId", "edit"],
    },

    {
      label: "Batch Inventori",
      segment: ["obat", "batch-inventori"],
    },
    {
      label: "Inventori Obat",
      segment: ["obat", "inventori-obat"],
    },
  ],
};

export default siteConfig;
