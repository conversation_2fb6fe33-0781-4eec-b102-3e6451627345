import * as yup from "yup";
export const addSubParameterYup = yup.object({
  jenis: yup.string().required("<PERSON>is wajib diisi"),
  nilai: yup.string().required("<PERSON><PERSON> wajib diisi"),
  satuan: yup.string().required("Satuan wajib diisi"),
  nilaiRujukan: yup.string().required("pilih satu Nilai Rujukan"),
});

export type addSubParameterYup = yup.InferType<typeof addSubParameterYup>;

export const addTitleYup = yup.object({
  name: yup.string().required("<PERSON>a layanan wajib diisi"),
});

export type addTitleYup = yup.InferType<typeof addTitleYup>;
