"use client";

import { FilterAutocomplete } from "@/components/custom/autocomplete";
import { FilterDatePicker } from "@/components/custom/datepicker";
import { setSearchParams } from "@/libs/search-params";
import { AutocompleteItem } from "@heroui/react";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";

type Props = {};

const majors = [
  { key: "1", value: "arsat" },
  { key: "2", value: "teguh" },
  { key: "3", value: "maulana" },
];

export default function FilterTable(props: Props) {
  const router = useRouter();
  const formik = useFormik<{
    idUser: string;
    date: any;
  }>({
    initialValues: {
      idUser: "",
      date: null,
    },
    onSubmit: (values: { idUser: string }) => {
      const filterParams = setSearchParams(values);
      router.replace(`?${filterParams.toString()}`);
    },
  });

  return (
    <div className="bg-default-50 p-5 rounded-md">
      <div className="flex flex-col gap-6">
        <div className="flex flex-col gap-2">
          <h1 className="text-xl font-medium">Filter Data</h1>
          <p className="text-sm text-neutral-500">
            Digunakan untuk menyaring data spesifik yang ingin dicari
          </p>
        </div>
        <div className="flex items-center  gap-6 w-full">
          <div className="flex flex-col gap-1 w-full">
            <label htmlFor="major_code" className="text-sm ">
              No. Batch
            </label>
            <FilterAutocomplete
              name="idUser"
              submitOnChange
              classNames={{ base: "bg-white dark:bg-inherit" }}
              formik={formik}
              placeholder={`ketik no. batch`}
              radius="sm"
              variant="bordered"
            >
              {majors.map((unit) => (
                <AutocompleteItem key={unit.key}>{unit.value}</AutocompleteItem>
              ))}
            </FilterAutocomplete>
          </div>
          <div className="flex flex-col gap-1 w-full">
            <label htmlFor="major_code" className="text-sm ">
              Tanggal Perolehan
            </label>
            <FilterDatePicker name="date" formik={formik} />
          </div>
        </div>
      </div>
    </div>
  );
}
