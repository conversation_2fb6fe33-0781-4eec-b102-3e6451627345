"use client";

import { SsoResponse } from "@psi/sso-auth";
import { ReactNode, createContext, useContext } from "react";

export const UserContext = createContext<SsoResponse | null>(null);

type Props = {
  children?: ReactNode;
  user: SsoResponse | null;
};
export default function UserProvider({ children, user }: Props) {
  return <UserContext.Provider value={user}>{children}</UserContext.Provider>;
}

export const useUser = () => {
  const context = useContext(UserContext);

  // if (!context) {
  //   throw new Error("useUser must be used within a UserProvider");
  // }

  return context;
};
