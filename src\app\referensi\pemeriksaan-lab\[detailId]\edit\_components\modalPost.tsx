"use client";

import { FormInput } from "@/components/custom/input";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>ody,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalHeader,
  Radio,
  RadioGroup,
  useDisclosure,
} from "@heroui/react";
import { useFormik } from "formik";
import { Plus } from "lucide-react";
import { addSubParameterYup } from "../schema/addLayanan";
import { subParameter } from "./formAddLayanan";

type payload = {
  jenis: string;
  nilai: string;
  satuan: string;
  nilaiRujukan: string;
  nilai2?: string | null;
};

export default function ModalSubList({
  idParameters,
  addSubParameter,
}: {
  idParameters: string;
  addSubParameter: (
    parameterId: string,
    updatedData: Partial<subParameter>
  ) => void;
}) {
  const { isOpen, onOpen, onOpenChange } = useDisclosure(); // state modals

  const initialValues = {
    jenis: "",
    nilai: "",
    satuan: "",
    nilaiRujukan: "",
  };

  const handleSubmit = async (value: subParameter) => {
    alert(JSON.stringify(value));
    // addSubParameter(idParameters, value);
    // formik.resetForm();
    // onOpenChange();
  };

  const formik = useFormik<payload>({
    initialValues,
    enableReinitialize: true,
    validationSchema: addSubParameterYup,
    onSubmit: handleSubmit,
  });

  const choiceRujukan = () => {
    switch (formik.values.nilaiRujukan) {
      case "1":
        return (
          <div className="flex items-center gap-2 p-4 rounded-md bg-semantic-info-50">
            <div className="flex flex-col gap-1">
              <label htmlFor="nilai" className="text-[13px] ">
                {" "}
                Nilai Minumum
              </label>
              <FormInput
                style={{border:"10px"}}
                isClearable={false}
                name={"nilai"}
                isNumeric={true}
                formik={formik}
                placeholder="masukan nilai"
              />
            </div>
            <div className="flex flex-col gap-1">
              <label htmlFor="nilai" className="text-[13px] ">
                {" "}
                Nilai Maximum
              </label>
              <FormInput
                isClearable={false}
                name={"nilai2"}
                isNumeric={true}
                formik={formik}
                placeholder="masukan nilai"
              />
            </div>
          </div>
        );
      case "2":
        return (
          <div className=" p-4 rounded-md bg-semantic-info-50">
            <div className="flex flex-col gap-1">
            <label htmlFor="nilai" className="text-sm ">
              {" "}
              Nilai Minimum
            </label>
            <FormInput
              isClearable={false}
              name={"nilai"}
              isNumeric={true}
              formik={formik}
              placeholder="masukan nilai"
            />
          </div>
          </div>
        );
      case "3":
        return (
          <div className=" p-4 rounded-md bg-semantic-info-50">
            <div className="flex flex-col gap-1">
            <label htmlFor="nilai" className="text-sm ">
              {" "}
              Nilai Tetap
            </label>
            <FormInput
              isClearable={false}
              name={"nilai"}
              isNumeric={true}
              formik={formik}
              placeholder="masukan nilai"
            />
          </div>
          </div>
        );
      case "4":
        return (
          <div className=" p-4 rounded-md bg-semantic-info-50">
            <div className="flex flex-col gap-1">
            <label htmlFor="nilai" className="text-sm ">
              {" "}
              Nilai Maksimum
            </label>
            <FormInput
              isClearable={false}
              name={"nilai"}
              isNumeric={true}
              formik={formik}
              placeholder="masukan nilai"
            />
          </div>
          </div>
        );
    }
  };

  return (
    <>
      <Button
        startContent={<Plus className="w-4" />}
        color="default"
        className="bg-default-50 border-2"
        size="sm"
        onPress={onOpenChange}
      >
        Tambah Jenis Pemeriksaan
      </Button>
      <Modal
        size="lg"
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        backdrop={"blur"}
      >
        <ModalContent>
          {(onClose) => (
            <>
              <form onSubmit={formik.handleSubmit}>
                <ModalHeader className="flex flex-col gap-1">
                  Tambah jenis pemeriksaan
                </ModalHeader>
                <ModalBody>
                  <div className="flex flex-col gap-6">
                    <div className="flex flex-col gap-1">
                      <label htmlFor="jenis" className="text-sm ">
                        {" "}
                        Jenis Pemeriksaan
                      </label>
                      <FormInput
                        isClearable={false}
                        name={"jenis"}
                        isNumeric={false}
                        formik={formik}
                        placeholder="masukan jenis"
                      />
                    </div>

                    <div className="flex flex-col gap-4">
                      <RadioGroup
                        label="Nilai Rujukan"
                        className="text-sm "
                        size="sm"
                        name="nilaiRujukan"
                        onValueChange={(value) =>
                          formik.setFieldValue("nilaiRujukan", value)
                        }
                      >
                        <Radio value="1">Range</Radio>
                        <Radio value="2">&ge; (Lebih dari sama dengan)</Radio>
                        <Radio value="3">{`== (Sama dengan)`}</Radio>
                        <Radio value="4">&le; (Kurang dari sama dengan)</Radio>
                      </RadioGroup>
                    </div>

                    <div className="">{choiceRujukan()}</div>

                    <div className="flex flex-col gap-1">
                      <label htmlFor="satuan" className="text-sm ">
                        {" "}
                        Satuan
                      </label>
                      <FormInput
                        isClearable={false}
                        name={"satuan"}
                        isNumeric={false}
                        formik={formik}
                        placeholder="masukan Satuan"
                      />
                      <span className="text-default-400 text-[11px]">ex: g/dL</span>
                    </div>
                  </div>
                </ModalBody>
                <ModalFooter>
                  <Button
                    className="bg-default-50 border-[1px] border-md"
                    onPress={onClose}
                  >
                    Kembali
                  </Button>
                  <Button
                    color="primary"
                    type="submit"
                    isLoading={formik.isSubmitting}
                  >
                    Simpan
                  </Button>
                </ModalFooter>
              </form>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
