"use client";

import {
  Pagination,
  Select,
  SelectItem,
  Table,
  TableProps,
} from "@heroui/react";
import { cn } from "@heroui/theme";
import { ReactNode } from "react";

type Props = {
  children?: ReactNode;
  bordered?: boolean;
  parameter?: boolean;
  pagination?: {
    page: number;
    pageSize: number;
    totalPage: number;
    totalData: number;
    onChangePage?: (page: number, pageSize: number) => void;
  };
} & TableProps;
export default function UTable({
  children,
  bordered,
  pagination,
  parameter = false,
  ...res
}: Props) {
  return (
    <>
      <Table
        isCompact
        classNames={{
          wrapper: `${
            !parameter
              ? "border-1 border-default-200 p-0 shadow-sm"
              : "shadow-none bg-default-50 p-0 border-[1px]"
          }`,
          th: "first:rounded-none last:rounded-none !py-4",
          td: cn("first:before:rounded-none last:before:rounded-none !py-2 ", {
            "border-b-1 border-default-200": bordered,
          }),
        }}
        {...res}
      >
        {children}
      </Table>
      {pagination && (
        <div className="flex justify-end p-4 gap-4 items-center">
          <Pagination
            showControls
            showShadow
            color="secondary"
            page={pagination.page}
            total={pagination?.totalPage ?? 0}
            variant="light"
            onChange={(page: number) => {
              if (pagination?.onChangePage) {
                pagination.onChangePage(page, pagination.pageSize);
              }
            }}
          />
          <Select
            aria-labelledby="div"
            className="w-28"
            classNames={{
              mainWrapper: "shadow-none !h-9",
              trigger: "h-9 min-h-9",
            }}
            defaultSelectedKeys={[pagination.pageSize.toString()]}
            variant="bordered"
            onChange={(e) => {
              // setPageSize(Number(e.target.value));
              if (pagination?.onChangePage) {
                pagination.onChangePage(
                  pagination.page,
                  Number(e.target.value)
                );
              }
            }}
          >
            <SelectItem key="10">10 / page</SelectItem>
            <SelectItem key="20">20 / page</SelectItem>
            <SelectItem key="50">50 / page</SelectItem>
            <SelectItem key="100">100 / page</SelectItem>
          </Select>
          <div className="flex gap-2 items-center">
            <div className="border-2 border-default-200 px-2 h-9 rounded-md flex items-center">
              {pagination?.totalData ?? "-"}
            </div>{" "}
            Total
          </div>
        </div>
      )}
    </>
  );
}
