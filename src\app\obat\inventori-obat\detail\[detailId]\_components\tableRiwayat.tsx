"use client";

import TablePoliklinik from "@/components/table/tablePoliklinik";
import { PropsTable } from "@/interfaces/tables";
import { ArrowDown, ArrowUp } from "lucide-react";
import { useCallback } from "react";

const data = [
  {
    id: 1,
    tglPencatatan: "12/12/2021",
    batch: "123456789",

    jumlah: "10",
    aksi: "1", // 1 = masuk, 2 = keluar
    jenisPencatatan: "Ditambahkan oleh Farmasi",
    ttd: "John Doe",
  },
  {
    id: 2,
    tglPencatatan: "12/12/2021",
    batch: "123456789",

    jumlah: "10",
    aksi: "2",
    jenisPencatatan: "Ditambahkan oleh Pasien",
    ttd: "John Doe",
  },
];

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "tglPencatatan",
    label: "Tanggal Pencatatan",
  },
  {
    key: "batch",
    label: "Batch",
  },

  {
    key: "jumlah",
    label: "<PERSON><PERSON><PERSON>",
  },
  {
    key: "aksi",
    label: "Aksi",
  },
  {
    key: "jenisPencatatan",
    label: "Jen<PERSON> Pencatatan",
  },
];

type Props = {
  poli: any;
  onChangeSwitch?: (id: number) => void;
};

export default function TableRiwayat() {
  const result: any[] = data;

  const renderCell = useCallback(
    (category: any, column_key: any, index: number) => {
      switch (column_key) {
        case "no":
          return index + 1;
        case "tglPencatatan":
          return <p className="lg:w-[200px]  ">{category.tglPencatatan}</p>;
        case "batch":
          return <p className="lg:w-[200px]  ">{category.batch}</p>;
        case "jumlah":
          return <p className="lg:w-[200px]  ">{category.jumlah}</p>;
        case "aksi":
          return category.aksi === "1" ? (
            <div className="flex gap-1 items-center text-primary-600 lg:w-[200px]  ">
              <ArrowDown className="w-3 font-medium" />
              <p className="capitalize">Masuk</p>
            </div>
          ) : (
            <div className="flex gap-1 items-center text-semantic-error-600">
              <ArrowUp className="w-3 font-medium " />
              <p className="capitalize">Keluar (Resep)</p>
            </div>
          );
        case "jenisPencatatan":
          return (
            <div className="flex flex-col gap-1">
              <p className="capitalize">{category.jenisPencatatan}</p>
              <p className="text-sm text-default-500">({category.ttd})</p>
            </div>
          );
        default:
          return category[column_key];
      }
    },
    []
  );

  const props: PropsTable<any> = {
    columns,
    renderCell,
    data: result,
    basePath: "/referensi/poli",
    parameter: true,
    isTab: true,
  };
  return (
    <div className=" rounded-md flex flex-col gap-6 pt-6">
      <div className="flex justify-between items-center">
        <h1 className="font-medium lg:text-xl">Riwayat Pemanfaatan Obat</h1>
      </div>
      <TablePoliklinik {...props} />
    </div>
  );
}
