"use client";
import {
  Navbar,
  NavBody,
  NavItems,
  MobileNav,
  NavbarLogo,
  NavbarButton,
  MobileNavHeader,
  MobileNavToggle,
  MobileNavMenu,
} from "@/components/ui/resizable-navbar";
import { ThemeSwitch } from "@/components/theme-switch";
import { Button } from "@heroui/react";
import { LogIn } from "lucide-react";
import { useState } from "react";
import UserSession from "../header/user-session";
import { Logo } from "../sidebar";

export function AppNavbar() {
  return (
    <div className="fixed  inset-0 rounded-none  hidden md:flex h-16 w-full bg-header-gradient dark:border-b-[2px] dark:bg-none border-zinc-500 z-[60]  items-center px-4 py-2 justify-between">
      <Logo />

      <div className="flex items-center gap-4">
        <ThemeSwitch />
        <UserSession />
      </div>
    </div>
  );
  return (
    <div className="relative w-full">
      <Navbar>
        {/* Desktop Navigation */}
        <NavBody className="fixed inset-x-0 top-0 z-50 w-full h-14 bg-header-gradient dark:bg-none">
          <div>Hello, Muhammad Isa Dadi Hasibuan</div>

          <div className="flex items-center gap-4">
            <ThemeSwitch />
            <UserSession />
          </div>
        </NavBody>
      </Navbar>

      {/* Navbar */}
    </div>
  );
}
