"use client";

import ButtonNavigation from "@/components/custom/buttonNavigation";
import TablePoliklinik from "@/components/table/tablePoliklinik";
import { PropsTable } from "@/interfaces/tables";
import { use, useCallback } from "react";

const data = [
  {
    id: 1,
    noBatch: "123456789",
    tglMasuk: "12/12/2021",
    jumlahMasuk: "10",
    sisa: "10",
    tglKadaluwarsa: "12/12/2021",
  },
  {
    id: 2,
    noBatch: "123456789",
    tglMasuk: "12/12/2021",
    jumlahMasuk: "10",
    sisa: "10",
    tglKadaluwarsa: "12/12/2021",
  },
];

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "noBatch",
    label: "No Batch",
  },
  {
    key: "tglMasuk",
    label: "Tanggal Masuk",
  },
  {
    key: "jumlahMasuk",
    label: "Jumlah Masuk",
  },
  {
    key: "sisa",
    label: "Si<PERSON>",
  },
  {
    key: "tglKadaluwarsa",
    label: "Tanggal Kadaluarsa",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

type Props = {
  poli: any;
  onChangeSwitch?: (id: number) => void;
};

export default function TablePencatatan() {
  const result: any[] = data;

  const renderCell = useCallback(
    (item: any, column_key: any, index: number) => {
      switch (column_key) {
        case "no":
          return index + 1;
        case "name":
          return <div className="">{item.name}</div>;
        case "active":
          return <div className="flex gap-2 justify-end items-center "></div>;
        case "action":
          return (
            <div className="flex items-center gap-2 justify-center">
              <ButtonNavigation
                href={`/obat/inventori-obat/detail/1/edit/${item.id}`}
                tema="secondary"
                icon="pencil"
                tooltip="Ubah Data"
                isIcon={true}
              />
              <ButtonNavigation
                href={`/obat/inventori-obat/detail/1/adjustment/${item.id}`}
                tema="yellow"
                icon="package"
                tooltip="Penyesuaian Data"
                isIcon={true}
              />
            </div>
          );
        default:
          return item[column_key];
      }
    },
    []
  );

  const props: PropsTable<any> = {
    columns,
    renderCell,
    data: result,
    basePath: "/referensi/poli",
    parameter: true,
    isTab: true,
  };
  return (
    <div className=" rounded-md flex flex-col gap-6 pt-6">
      <div className="flex justify-between items-center">
        <h1 className="font-medium lg:text-xl">Daftar Pencatatan Obat</h1>
        <ButtonNavigation
          href={`/obat/inventori-obat/detail/1/add`}
          tema="light"
          icon="plus"
          tooltip="Tambah Data"
          title="Obat Masuk"
        />
      </div>
      <TablePoliklinik {...props} />
    </div>
  );
}
