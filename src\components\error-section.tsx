"use client";
import { <PERSON><PERSON>, <PERSON>, CardBody } from "@heroui/react";
import { ReactNode } from "react";
import { useRouter } from "next/navigation";
import { DynamicIcon as Icon } from "lucide-react/dynamic";

type Props = {
  title?: string;
  message?: string;
  code?: number;
  icon?: ReactNode;
  showIcon?: boolean;
  showButton?: boolean;
};

export default function ErrorSection({
  title,
  code,
  message,
  icon,
  showIcon = true,
  showButton,
}: Props) {
  const router = useRouter();

  const template: Record<number, { title: string; message: string }> = {
    403: {
      title: "Forbidden",
      message: "Anda tidak memeiliki izin untuk mengakses fitur ini",
    },
    404: {
      title: "Not Found",
      message: "Halaman yang kamu kunjungi tidak ditemukan",
    },
    500: {
      title: "Inernal Server Error",
      message: "Terjadi kesalahan saat mengakses fitur",
    },
  };

  const onRefresh = () => {
    router.refresh();
  };

  return (
    <Card radius="sm" shadow="none">
      <CardBody>
        <div className="px-8 py-4">
          <div className="flex flex-wrap flex-col justify-center items-center gap-2">
            {showIcon
              ? (icon ?? (
                  <Icon className="w-12 h-12 text-danger-500" name="search-x" />
                ))
              : null}

            <h3 className="font-medium text-lg md:text-xl text-center">
              {title}
              {!title && code && template[code] && template[code].title}
            </h3>

            <p className="font-normal text-base md:text-lg text-default-400 mb-3 text-center">
              {message}
              {!message && code && template[code].message}
            </p>

            {showButton && (
              <div className="flex gap-2 flex-wrap">
                <Button variant="flat" onPress={onRefresh}>
                  <Icon className="h-4" name="refresh-ccw" />
                  Refresh
                </Button>
              </div>
            )}
          </div>
        </div>
      </CardBody>
    </Card>
  );
}
