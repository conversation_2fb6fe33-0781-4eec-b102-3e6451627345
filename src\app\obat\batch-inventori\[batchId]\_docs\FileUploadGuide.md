# Panduan Kustomisasi File Upload

## Overview
Komponen `FormUpload` telah diupdate untuk mendukung berbagai style dengan tombol "Choose File" yang bisa diposisikan di kanan dan custom text placeholder.

## Fitur Utama

### 1. **Tombol di Kanan**
- Tombol "Choose File" berada di sebelah kanan input
- Text placeholder custom: "Tidak ada file dipilih"
- Menampilkan nama file yang dipilih

### 2. **Custom Placeholder**
- Default: "Tidak ada file dipilih"
- Bisa dikustomisasi sesuai kebutuhan
- Otomatis berubah menjadi nama file ketika file dipilih

### 3. **Multiple Variants**
- `default`: Style original dengan tombol di kiri
- `rightButton`: Tombol di kanan dengan style sederhana
- `modernRight`: Style modern dengan tombol di kanan
- `elegantRight`: Style elegant dengan gradient button

## Cara Penggunaan

### Basic Usage (Tombol Kanan)

```tsx
<FormUpload
  name="document"
  formik={formik}
  variant="rightButton"
  placeholder="Tidak ada file dipilih"
  accept=".pdf,.doc,.docx"
/>
```

### Modern Style (Tombol Kanan)

```tsx
<FormUpload
  name="image"
  formik={formik}
  variant="modernRight"
  placeholder="Belum ada gambar dipilih"
  accept="image/*"
/>
```

### Elegant Style (Tombol Kanan)

```tsx
<FormUpload
  name="certificate"
  formik={formik}
  variant="elegantRight"
  placeholder="Tidak ada sertifikat dipilih"
  accept=".pdf,.jpg,.png"
/>
```

### Custom Style

```tsx
<FormUpload
  name="attachment"
  formik={formik}
  customClasses={{
    label: "font-bold text-purple-700",
    input: "relative flex w-full text-sm py-4 px-4 border-2 border-purple-300 rounded-xl cursor-pointer hover:border-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 bg-purple-50 hover:bg-purple-100 file:absolute file:right-3 file:top-1/2 file:-translate-y-1/2 file:px-6 file:py-2 file:border-0 file:bg-purple-600 file:text-white file:text-sm file:font-bold file:rounded-lg hover:file:bg-purple-700 file:cursor-pointer pr-32"
  }}
  placeholder="Pilih file dengan style custom..."
/>
```

## Penjelasan CSS Classes

### Key Classes untuk Tombol Kanan:

1. **Container Input:**
   - `relative`: Untuk positioning absolute button
   - `flex`: Display flex
   - `pr-24` / `pr-28` / `pr-32`: Padding right untuk space button

2. **File Button (prefix `file:`):**
   - `file:absolute`: Positioning absolute
   - `file:right-0` / `file:right-2` / `file:right-3`: Posisi dari kanan
   - `file:top-1/2 file:-translate-y-1/2`: Center vertical
   - `file:px-4 file:py-2`: Padding button
   - `file:bg-blue-600 file:text-white`: Warna button
   - `hover:file:bg-blue-700`: Hover effect

3. **Text Display:**
   - `absolute left-3 top-1/2 -translate-y-1/2`: Posisi text
   - `pointer-events-none`: Tidak bisa diklik
   - `text-gray-500`: Warna text placeholder

## Variants Available

### 1. `rightButton`
- Style sederhana dengan tombol biru di kanan
- Border abu-abu dengan hover effect
- Cocok untuk form standar

### 2. `modernRight`
- Border lebih tebal dengan rounded corners
- Tombol lebih kecil dan modern
- Transition effects yang smooth

### 3. `elegantRight`
- Background dengan warna emerald
- Tombol dengan gradient effect
- Shadow effects untuk tampilan premium

## Props Interface

```tsx
type FormUploadProps<T> = {
  name: NestedKeyOf<T>;
  formik: FormikProps<T>;
  variant?: "default" | "rightButton" | "modernRight" | "elegantRight";
  placeholder?: string;
  customClasses?: {
    label?: string;
    input?: string;
  };
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  // ... other InputProps
};
```

## Contoh Lengkap

Lihat file `FileUploadExample.tsx` untuk contoh implementasi lengkap dengan berbagai style dan preview file yang dipilih.

## Tips Kustomisasi

1. **Mengatur Lebar Button:** Sesuaikan `pr-24`, `pr-28`, `pr-32` untuk space button
2. **Warna Custom:** Ganti `file:bg-blue-600` dengan warna yang diinginkan
3. **Border Style:** Ubah `border`, `border-2`, `border-dashed` sesuai kebutuhan
4. **Hover Effects:** Tambahkan `hover:` prefix untuk interactive states
5. **Focus States:** Gunakan `focus:ring-2 focus:ring-color` untuk accessibility

## Backward Compatibility

Komponen tetap mendukung style lama dengan menggunakan `variant="default"` atau tanpa menentukan variant.
