"use client";

import { FormInput } from "@/components/custom/input";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  useDisclosure,
} from "@heroui/react";
import { useFormik } from "formik";
import { Eye, Pencil } from "lucide-react";
import { useState } from "react";
import useS<PERSON> from "swr";
import ApiResponse from "@/interfaces/api-response";
import { getPoli } from "../actions";
import { layananPoliPost } from "../schema/postLayananPoli";

type payload = {
  laboratorium: string;
};

export default function Modals({ id }: { id: string }) {
  const [isSubmit, setIsSubmit] = useState(false);
  const { isOpen, onOpen, onOpenChange } = useDisclosure(); // state modals

  // swr
  const {
    data: poli,
    isLoading: poliLoading,
    mutate: mutate,
  } = useSWR<ApiResponse<any[]>>(
    id ? ["get_poli", id] : null,
    ([, id]) => getPoli(id as string),
    {
      revalidateIfStale: false,
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      shouldRetryOnError: false,
    }
  );

  const initialValues = {
    laboratorium: "",
  };

  const handleSubmit = (value: typeof initialValues) => {
    setIsSubmit(true);
    mutate();
    alert(JSON.stringify(value));
    setIsSubmit(false);
  };

  const formik = useFormik<payload>({
    initialValues,
    enableReinitialize: true,
    validationSchema: layananPoliPost,
    onSubmit: handleSubmit,
  });

  return (
    <>
      <Tooltip content="Edit Data">
        <Button isIconOnly color="secondary" size="sm" onPress={onOpenChange}>
          <Eye className="w-4" />
        </Button>
      </Tooltip>
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} backdrop={"blur"}>
        <ModalContent>
          {(onClose) => (
            <>
              <form onSubmit={formik.handleSubmit}>
                <ModalHeader className="flex flex-col gap-1">
                  Tambah Laboratorium
                </ModalHeader>
                <ModalBody className="mb-4">
                  <div className="flex flex-col gap-1">
                    <label htmlFor="laboratorium" className="text-sm ">
                      {" "}
                      Nama Laboratorium
                    </label>
                    <FormInput
                      name={"laboratorium"}
                      isNumeric={false}
                      formik={formik}
                      placeholder="Ketik nama laboratorium"
                    />
                  </div>
                </ModalBody>
                <ModalFooter>
                  <Button
                    className="bg-default-50 border-[1px] border-md"
                    onPress={onClose}
                  >
                    Kembali
                  </Button>
                  <Button color="primary" type="submit" isLoading={isSubmit}>
                    Simpan
                  </Button>
                </ModalFooter>
              </form>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
