"use client";

import { CheckboxIcon, cn } from "@heroui/react";
import { ChevronDownIcon, ChevronRightIcon } from "lucide-react";
import Tree, { TreeProps } from "rc-tree";

const CustomCheckbox = ({
  checked,
  isHalf,
  disableCheckbox,
}: {
  checked?: boolean;
  isHalf?: boolean;
  disableCheckbox?: boolean;
}) => {
  return (
    <span
      className={cn(
        "h-5 w-5  rounded border-2 border-default-300 inline-block",
        { "bg-primary-500": checked || isHalf },
        { "bg-default-200 cursor-not-allowed": disableCheckbox }
      )}
    >
      <CheckboxIcon
        className={cn("scale-75", { "text-white": checked || isHalf })}
        isIndeterminate={isHalf}
        isSelected={checked}
      />
    </span>
  );
};

export default function CheckboxTree(props: TreeProps) {
  return (
    <Tree
      checkable
      showLine
      icon={({ checked, halfChecked, disableCheckbox }) => (
        <CustomCheckbox
          checked={checked}
          disableCheckbox={disableCheckbox}
          isHalf={halfChecked}
        />
      )}
      selectable={false}
      switcherIcon={(obj) => {
        if (obj.isLeaf) {
          return null;
        }

        if (obj.expanded) {
          return <ChevronDownIcon size={18} />;
        }

        return <ChevronRightIcon size={18} />;
      }}
      titleRender={(node) => (
        <span className="pl-2 text-sm absolute left-6 top-0">
          {node.title?.toString()}
        </span>
      )}
      {...props}
    />
  );
}
