export const classNames = {
  fileUpload: {
    // Style default (tombol kiri)
    default: {
      label: "font-medium text-gray-700",
      input:
        "block w-full text-sm file:mr-4 py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-pink-50 file:text-pink-700 hover:file:bg-pink-100 cursor-pointer",
    },

    // Style dengan tombol di kanan dan custom text
    rightButton: {
      label: "font-medium text-gray-700 mb-2",
      input:
        "relative flex w-full text-sm py-2 px-3 border border-gray-300 rounded-md cursor-pointer hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 file:absolute file:right-0 file:top-0 file:bottom-0 file:px-4 file:py-2 file:border-0 file:bg-blue-600 file:text-white file:font-medium file:rounded-r-md hover:file:bg-blue-700 file:cursor-pointer pr-24",
    },

    // Style modern dengan tombol kanan
    modernRight: {
      label: "font-medium text-slate-700 mb-2",
      input:
        "relative flex w-full text-sm py-[10px] px-4 border-[1px] shadow-sm border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 file:absolute file:right-2 file:top-1/2 file:-translate-y-1/2 file:px-4 file:py-1.5 file:border-0 file:bg-blue-600 file:text-white file:text-xs file:font-semibold file:rounded-md hover:file:bg-blue-700 file:cursor-pointer pr-28",
    },

    // Style elegant dengan tombol kanan
    elegantRight: {
      label: "font-medium text-emerald-700 mb-2",
      input:
        "relative flex w-full text-sm py-3 px-4 border border-emerald-200 rounded-lg cursor-pointer hover:border-emerald-300 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200 bg-emerald-50 hover:bg-emerald-100 file:absolute file:right-3 file:top-1/2 file:-translate-y-1/2 file:px-5 file:py-2 file:border-0 file:bg-gradient-to-r file:from-emerald-500 file:to-emerald-600 file:text-white file:text-xs file:font-semibold file:rounded-full hover:file:from-emerald-600 hover:file:to-emerald-700 file:shadow-sm hover:file:shadow-md file:cursor-pointer pr-32",
    },

    // Backward compatibility
    label: "font-medium",
    input:
      "block w-full text-sm file:mr-4 py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-pink-50 file:text-pink-700",
  },
  input: {
    label: "font-medium text-default-700",
    inputWrapper: "bg-white dark:bg-inherit",
    // inputWrapper:
    //   "group-data-[focus=true]:ring-primary-300 group-data-[focus=true]:ring-2  group-data-[focus=true]:border-default-200 data-[hover=true]:border-primary-200",
  },
} as const;

export const baseUrl = process.env.NEXT_PUBLIC_APPS_URL ?? "";

export const FALLBACK_PROFILE = `/default-profile.png`;

export const PATIENT_TYPE_LIST = [
  { value: "mahasiswa", label: "Mahasiswa" },
  { value: "pegawai", label: "Pegawai" },
  { value: "keluarga-pegawai", label: "Keluarga Pegawai" },
  { value: "umum", label: "Umum" },
  { value: "alumni", label: "Alumni" },
  { value: "pensiunan", label: "Pensiunan" },
];

export const PATIENT_TYPE = {
  mahasiswa: "Mahasiswa",
  pegawai: "Pegawai",
  "keluarga-pegawai": "Keluarga Pegawai",
  umum: "Umum",
  alumni: "Alumni",
  pensiunan: "Pensiunan",
};
