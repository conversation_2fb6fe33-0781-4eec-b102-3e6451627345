import { createLogger, format, transports } from "winston";
import "winston-daily-rotate-file";

const timezoned = () => {
  return new Date().toLocaleString("id-ID", {
    timeZone: "Asia/Jakarta",
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false,
  });
};

const getLogger = (fileName = "application") => {
  const fileLogTransport = new transports.DailyRotateFile({
    filename: `log/${fileName}-%DATE%.log`,
    datePattern: "YYYY-MM-DD",
    zippedArchive: false,
    maxSize: "20m",
    maxFiles: "30d",
  });

  const logger = createLogger({
    level: "info",
    format: format.combine(
      format.timestamp({ format: timezoned }),
      format.printf(({ level, message, timestamp }) => {
        return `${timestamp} [${level.toUpperCase()}]: ${message} `;
      })
    ),
    defaultMeta: { service: "DLC" },
  });

  logger.add(fileLogTransport);

  return logger;
};

export default getLogger();
