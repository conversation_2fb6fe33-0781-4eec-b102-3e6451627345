"use client";
import signOut from "@/libs/sign-out";
import { Button } from "@heroui/react";
import { useRouter } from "next/navigation";

type Props = {
  title?: string;
  message?: string;
  btnText?: string;
  onClick?: () => void;
  code?: 403 | 404 | 500 | number;
};

export default function ErrorPage({
  title,
  message,
  btnText,
  onClick,
  code,
}: Props) {
  const router = useRouter();
  const template: Record<number, { title: string; message: string }> = {
    403: {
      title: "Forbidden",
      message: "Anda tidak memeiliki izin untuk mengakses halaman ini",
    },
    404: {
      title: "Not Found",
      message: "Halaman yang kamu kunjungi tidak ditemukan",
    },
    500: {
      title: "Inernal Server Error",
      message: "Te<PERSON><PERSON><PERSON> kesalahan saat mengakses halaman",
    },
  };

  return (
    <div className="h-screen px-8 justify-between items-center flex">
      <div className="flex-1">
        {code && <h3 className="text-4xl text-primary-main">{code}</h3>}
        <h3 className="font-medium text-3xl">
          {title}
          {!title && code && template[code] && template[code].title}
        </h3>

        <p className="font-normal text-xl text-neutral-60 mb-3">
          {message}
          {!message && code && template[code].message}
        </p>

        <div className="flex gap-2 flex-wrap">
          <Button
            variant="bordered"
            onPress={() => (onClick ? onClick() : router.back())}
          >
            {btnText ?? "kembali"}
          </Button>

          <Button variant="ghost" color="danger" onPress={() => signOut()}>
            Logout
          </Button>
        </div>
      </div>
    </div>
  );
}
