"use client";
import React, { useState, createContext, useContext, useEffect } from "react";
import { AnimatePresence, motion } from "motion/react";
import { cn } from "@heroui/theme";
import { Menu, X } from "lucide-react";
import { RouteItem } from "@/types/site-config";
import Link from "next/link";
import { usePathname } from "next/navigation";
import UserSession from "@/app/_components/header/user-session";
import { ThemeSwitch } from "../theme-switch";
import { Logo } from "@/app/_components/sidebar";

interface SidebarContextProps {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  animate: boolean;
}

const SidebarContext = createContext<SidebarContextProps | undefined>(
  undefined
);

export const useSidebar = () => {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error("useSidebar must be used within a SidebarProvider");
  }
  return context;
};

export const SidebarProvider = ({
  children,
  open: openProp,
  setOpen: setOpenProp,
  animate = true,
}: {
  children: React.ReactNode;
  open?: boolean;
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>;
  animate?: boolean;
}) => {
  const [openState, setOpenState] = useState(false);

  const open = openProp !== undefined ? openProp : openState;
  const setOpen = setOpenProp !== undefined ? setOpenProp : setOpenState;

  return (
    <SidebarContext.Provider value={{ open, setOpen, animate: animate }}>
      {children}
    </SidebarContext.Provider>
  );
};

export const Sidebar = ({
  children,
  open,
  setOpen,
  animate,
}: {
  children: React.ReactNode;
  open?: boolean;
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>;
  animate?: boolean;
}) => {
  return (
    <SidebarProvider open={open} setOpen={setOpen} animate={animate}>
      {children}
    </SidebarProvider>
  );
};

export const SidebarBody = (props: React.ComponentProps<typeof motion.div>) => {
  return (
    <>
      <DesktopSidebar {...props} />
      <MobileSidebar {...(props as React.ComponentProps<"div">)} />
    </>
  );
};

export const DesktopSidebar = ({
  className,
  children,
  ...props
}: React.ComponentProps<typeof motion.div>) => {
  const { open, setOpen, animate } = useSidebar();
  return (
    <div className="relative">
      <motion.div
        className={cn(
          "fixed h-full top-16 pl-4 py-4 hidden md:flex md:flex-col bg-white dark:bg-default-100 w-[58px] shrink-0 z-50 ",
          {
            "hover:bg-opacity-75 hover:dark:bg-opacity-75 backdrop-blur-sm  px-2  hover:pr-0":
              animate,
          },

          className
        )}
        animate={{
          width: animate ? (open ? "260px" : "58px") : "260px",
        }}
        // initial={{
        //   width: "260px",
        // }}
        onMouseEnter={() => setOpen(true)}
        onMouseLeave={() => setOpen(false)}
        {...props}
      >
        {children}
      </motion.div>
    </div>
  );
};

export const MobileSidebar = ({
  className,
  children,
  ...props
}: React.ComponentProps<"div">) => {
  const { open, setOpen } = useSidebar();
  const pathname = usePathname();

  useEffect(() => {
    setOpen(false);
  }, [pathname]);
  return (
    <>
      <div
        className={cn(
          "fixed h-14 shadow-md inset-0 z-10 px-4 py-4 flex flex-row md:hidden  items-center justify-between bg-header-gradient dark:bg-none dark:bg-neutral-800 w-full"
        )}
        {...props}
      >
        <div className="flex justify-between z-20 w-full">
          <div className="inline-flex gap-4 items-center">
            <Menu
              className="text-neutral-800 dark:text-neutral-200"
              onClick={() => setOpen(!open)}
            />

            <Logo />
          </div>

          <div className="inline-flex items-center gap-5">
            <ThemeSwitch />
            <UserSession />
          </div>
        </div>
        <AnimatePresence>
          {open && (
            <motion.div
              initial={{ x: "-100%", opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: "-100%", opacity: 0 }}
              transition={{
                duration: 0.3,
                ease: "easeInOut",
              }}
              className={cn(
                "fixed h-full w-full inset-0 bg-white dark:bg-neutral-900 p-10 z-[100] flex flex-col justify-between",
                className
              )}
            >
              <div
                className="absolute right-10 top-10 z-50 text-neutral-800 dark:text-neutral-200"
                onClick={() => setOpen(!open)}
              >
                <X />
              </div>
              {children}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </>
  );
};

export const SidebarLink = ({
  item,
  className,
  isActive,
  ...props
}: {
  item: RouteItem;
  className?: string;
  isActive?: boolean;
}) => {
  const { open, animate } = useSidebar();

  return (
    <Link href={item.path}>
      <div
        className={cn(
          "w-full rounded-l-lg !no-underline hover:bg-primary-50  bg-opacity-75 dark:hover:bg-slate-700 h-9 gap-2 flex items-center",
          "flex items-center justify-start gap-2  group/sidebar-item pl-1",
          {
            "  bg-semantic-primary-100 dark:bg-slate-500 text-primary-main dark:text-slate-300":
              isActive && open,
          },
          {
            "border-r-4 border-semantic-primary-600 dark:border-slate-700 ":
              isActive && (animate ? (open ? 1 : 0) : 1),
          },
          { ...props }
        )}
      >
        <span
          className={cn(
            "group-hover/sidebar-item:translate-x-1 transition duration-150",
            {
              "p-1": !open,
            },
            {
              "bg-semantic-primary-100 dark:bg-slate-500 text-primary-main dark:text-slate-300  rounded-md min-w-8 min-h-8":
                isActive && !open,
            }
          )}
        >
          {item.icon}
        </span>
        <motion.span
          animate={{
            display: animate ? (open ? "flex" : "none") : "inline-flex",
            opacity: animate ? (open ? 1 : 0) : 1,
          }}
          className="text-neutral-700 flex gap-2 font-bold dark:text-neutral-200 text-sm group-hover/sidebar-item:translate-x-1 transition duration-150 whitespace-pre  !p-0 !m-0"
        >
          {item.title}
        </motion.span>
      </div>
    </Link>
  );
};
