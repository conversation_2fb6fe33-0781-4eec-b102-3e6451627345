"use client";

import { FormInput } from "@/components/custom/input";
import {
  addToast,
  <PERSON><PERSON>,
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  useDisclosure,
} from "@heroui/react";
import { useFormik } from "formik";
import { Plus } from "lucide-react";
import { postLabYup } from "../_schema/postPoli";
import { postLab } from "../actions";

type payload = {
  name: string;
};

export default function ModalPost() {
  const { isOpen, onOpen, onOpenChange } = useDisclosure(); // state modals

  const initialValues = {
    name: "",
  };

  const handleSubmit = async (value: typeof initialValues) => {
    formik.setSubmitting(true);
    try {
      const res = await postLab({ name: value.name, active: null });
      if (res.error) {
        throw new Error(res?.message);
      }
      addToast({
        title: "Berhasil menambahkan data",
        color: "success",
      });
      window.location.reload();
    } catch (error: any) {
      addToast({
        title: "Error",
        color: "danger",
        description: error.message ?? "Ter<PERSON><PERSON> kes<PERSON>han",
      });
    } finally {
      formik.setSubmitting(false);
    }
  };

  const formik = useFormik<payload>({
    initialValues,
    enableReinitialize: true,
    validationSchema: postLabYup,
    onSubmit: handleSubmit,
  });

  return (
    <>
      <Tooltip content="Edit Data">
        <Button
          startContent={<Plus />}
          color="primary"
          size="sm"
          onPress={onOpenChange}
        >
          Lab
        </Button>
      </Tooltip>
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} backdrop={"blur"}>
        <ModalContent>
          {(onClose) => (
            <>
              <form onSubmit={formik.handleSubmit}>
                <ModalHeader className="flex flex-col gap-1">
                  Tambah Laboratorium
                </ModalHeader>
                <ModalBody className="mb-4">
                  <div className="flex flex-col gap-1">
                    <label htmlFor="laboratorium" className="text-sm ">
                      {" "}
                      Nama Laboratorium
                    </label>
                    <FormInput
                      name={"name"}
                      isNumeric={false}
                      formik={formik}
                      placeholder="Ketik nama laboratorium"
                    />
                  </div>
                </ModalBody>
                <ModalFooter>
                  <Button
                    className="bg-default-50 border-[1px] border-md"
                    onPress={onClose}
                  >
                    Kembali
                  </Button>
                  <Button
                    color="primary"
                    type="submit"
                    isLoading={formik.isSubmitting}
                  >
                    Simpan
                  </Button>
                </ModalFooter>
              </form>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
