@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
}

@layer components {
  .card-wrapper {
    @apply bg-white p-4 rounded-lg border border-zinc-100 dark:bg-zinc-900 dark:border-zinc-800 dark:text-zinc-200;
  }
}

@layer utilities {
}

.rc-tree .rc-tree-treenode span.rc-tree-switcher,
.rc-tree .rc-tree-treenode span.rc-tree-checkbox,
.rc-tree .rc-tree-treenode span.rc-tree-iconEle {
  background-image: none !important;
}

.rc-tree-checkbox {
  display: none !important;
}

.rc-tree-switcher {
  margin-right: 6px !important;
}

.rc-tree .rc-tree-treenode {
  display: flex;
  align-items: baseline;
}
.rc-tree-list-holder-inner {
  gap: 6px;
}
