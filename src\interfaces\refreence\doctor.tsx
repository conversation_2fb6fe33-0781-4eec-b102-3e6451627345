import { PaginatePoli } from "../api-response-poli";

export type ItemDoctor = {
  id: string;
  user_id: string;
  full_name: string;
  full_name_with_degree: string;
  employee_identification_number: string;
  poly_associations: [
    {
      id: string;
      name: string;
      slug: string;
      active: boolean;
    }
  ];
  schedules: [
    {
      id: string;
      doctor_id: string;
      poly_id: string;
      day: string;
      start_time: string;
      end_time: string;
    }
  ];
};

// Type untuk response API dokter dengan pagination
export type DoctorApiResponse = {
  content: ItemDoctor[];
} & PaginatePoli;
