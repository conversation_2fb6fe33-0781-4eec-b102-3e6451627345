"use client";

import { cn, Pagination, Select, SelectItem } from "@heroui/react";
import { flexRender, Row, Table } from "@tanstack/react-table";
import { ChevronDown, ChevronUp, NotepadTextDashed } from "lucide-react";
import React, { Fragment, ReactNode } from "react";

import Empty from "../empty";

export type MetaType = {
  rowSpan?: number;
  header?: {
    className?: string;
  };
  cell?: {
    className?: string;
  };
};

type Props<T> = {
  table: Table<T>;
  renderSubComponent?: ({ row }: { row: Row<T> }) => ReactNode;
  bordered?: boolean;
  showPagination?: boolean;
  noData?: boolean;
};

export default function TanTable<T>({
  table,
  renderSubComponent,
  showPagination = true,
  bordered,
  noData,
}: Props<T>) {
  if (noData) return <Empty title="Data tidak tersedia" />;

  return (
    <div>
      <div className="rounded-lg border border-default-300 overflow-x-auto">
        <table className="w-full border-collapse overflow-hidden text-sm text-default-900">
          <thead className="border-b border-default-300">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header, index) => (
                  <th
                    key={header.id}
                    className={cn(
                      `px-3 py-4 bg-default-50 dark:bg-default-100 font-medium ${index < 3 ? "text-left" : ""}`,
                      (header.column.columnDef.meta as MetaType)?.header
                        ?.className,
                    )}
                    colSpan={header.colSpan}
                  >
                    {header.isPlaceholder ? null : (
                      <div
                        aria-hidden="true"
                        className={cn({
                          "cursor-pointer select-none inline-flex gap-2":
                            header.column.getCanSort(),
                        })}
                        title={
                          header.column.getCanSort()
                            ? header.column.getNextSortingOrder() === "asc"
                              ? "Sort ascending"
                              : header.column.getNextSortingOrder() === "desc"
                                ? "Sort descending"
                                : "Clear sort"
                            : undefined
                        }
                        onClick={header.column.getToggleSortingHandler()}
                      >
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}

                        {{
                          asc: (
                            <div>
                              <ChevronUp className="h-4 w-4 text-default-500 stroke-[4px]" />
                              <ChevronDown className="h-4 w-4 text-default-500" />
                            </div>
                          ),
                          desc: (
                            <div>
                              <ChevronUp className="h-4 w-4 text-default-500" />
                              <ChevronDown className="h-4 w-4 text-default-500 stroke-[4px]" />
                            </div>
                          ),
                        }[header.column.getIsSorted() as string] ?? null}
                      </div>
                    )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody className="dark:bg-content1 bg-white  font-normal">
            {table.getRowCount() === 0 ? (
              <tr>
                <td
                  className="p-4 h-52"
                  colSpan={table.getAllFlatColumns().length}
                >
                  <div className="w-full inline-flex items-center justify-center flex-col gap-2">
                    <NotepadTextDashed className="h-14 w-14 stroke-default-500" />
                    <p className="text-default-700">Tidak ada data</p>
                  </div>
                </td>
              </tr>
            ) : (
              table.getRowModel().rows.map((row) => (
                <Fragment key={row.id}>
                  <tr>
                    {row.getVisibleCells().map((cell) => (
                      <td
                        key={cell.id}
                        className={cn(
                          `px-3 py-4`,
                          (cell.column.columnDef.meta as MetaType)?.cell
                            ?.className,
                          {
                            "border-b border-default-300": bordered,
                          },
                        )}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </td>
                    ))}
                  </tr>
                  {row.getIsExpanded() && renderSubComponent && (
                    <tr>
                      <td
                        className={cn(`p-2`, {
                          "border-b border-default-300": bordered,
                        })}
                        colSpan={row.getVisibleCells().length}
                      >
                        {renderSubComponent({ row })}
                      </td>
                    </tr>
                  )}
                </Fragment>
              ))
            )}
          </tbody>
        </table>
      </div>

      {showPagination && table.getRowCount() > 0 && (
        <div className="flex flex-wrap justify-end p-4 gap-2 items-center ">
          <Pagination
            showControls
            showShadow
            color="primary"
            page={table.getState().pagination.pageIndex + 1}
            total={table.getPageCount()}
            variant="light"
            onChange={(page: number) => table.setPageIndex(page - 1)}
          />
          <Select
            aria-labelledby="div"
            className="w-28"
            classNames={{
              mainWrapper: "shadow-none !h-9",
              trigger: "h-9 min-h-9",
            }}
            defaultSelectedKeys={[
              table.getState().pagination.pageSize.toString(),
            ]}
            radius="sm"
            variant="bordered"
            onChange={(e) => {
              table.setPageSize(Number(e.target.value));
            }}
          >
            <SelectItem key={10}>10 / page</SelectItem>
            <SelectItem key={20}>20 / page</SelectItem>
            <SelectItem key={50}>50 / page</SelectItem>
            <SelectItem key={100}>100 / page</SelectItem>
          </Select>
          <div className="flex gap-2 items-center">
            <div className="border-2 border-default-200 px-2 h-9 rounded-lg flex items-center">
              {table.getRowCount() ?? "-"}
            </div>{" "}
            Total
          </div>
        </div>
      )}
    </div>
  );
}
