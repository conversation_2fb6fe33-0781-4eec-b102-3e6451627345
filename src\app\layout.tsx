import "@/styles/globals.css";
import "rc-tree/assets/index.css";
import { Metadata, Viewport } from "next";
import clsx from "clsx";

import { Providers } from "./providers";

import { fontSans } from "@/config/fonts";
import ProgressBar from "@/components/progress-bar";
import siteConfig from "@/constant/site";
import SidebarSection from "./_components/sidebar";
import UserProvider from "@/provider/user";
import { SSOAuthentication } from "@/libs/auth";
import ErrorPage from "@/components/error-page";
import BreadcrumbsSection from "@/components/custom/breadcrumbs";

export const metadata: Metadata = {
  title: {
    default: siteConfig.name,
    template: `%s - ${siteConfig.name}`,
  },
  description: siteConfig.description,
  icons: {
    icon: "/favicon.ico",
  },
};

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" },
  ],
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const user = await SSOAuthentication();

  return (
    <html suppressHydrationWarning lang="en">
      <head />
      <body
        className={clsx(
          "bg-background font-sans antialiased",
          fontSans.variable
        )}
      >
        <UserProvider user={user.data}>
          <Providers themeProps={{ attribute: "class", defaultTheme: "light" }}>
            {user.error ? (
              <ErrorPage
                code={user.code}
                title="Gagal Autentikasi User"
                message={user.message}
              />
            ) : (
              <ProgressBar>
                <SidebarSection>
                  <BreadcrumbsSection />
                  {children}
                </SidebarSection>
              </ProgressBar>
            )}
          </Providers>
        </UserProvider>
        <footer className="text-center text-sm text-gray-500 dark:text-gray-400 py-4">
          <p>
            &copy; {new Date().getFullYear()} {siteConfig.name}. All rights
            reserved.
          </p>
        </footer>
      </body>
    </html>
  );
}
