"use client";

import TablePoliklinik from "@/components/table/tablePoliklinik";
import { PropsTable } from "@/interfaces/tables";
import { Chip } from "@heroui/react";
import { useCallback } from "react";
import Modals from "./modal";
import ModalPost from "./modalPost";
import { <PERSON><PERSON><PERSON>, itemDoctor } from "@/interfaces/refreence/doctor";

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "name",
    label: "Nama Tenaga Medis",
  },
  {
    key: "jenis",
    label: "<PERSON><PERSON>",
  },
  {
    key: "tugas",
    label: "Penugasan",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

export default function TableDokter({ doctors }: { doctors: <PERSON><PERSON><PERSON> }) {
  const data: itemDoctor[] = doctors?.content ?? [];
  const pageInfo = doctors?.page;

  const renderCell = useCallback(
    (item: any, column_key: any, index: number) => {
      switch (column_key) {
        case "no":
          return index + 1;
        case "name":
          return <div>{item.full_name_with_degree}</div>;
        case "tugas":
          return (
            <div className="flex flex-wrap  gap-2">
              {item.poly_associations.map((item: any, index: number) => (
                <Chip key={index} size="sm">
                  {item}
                </Chip>
              ))}
            </div>
          );
        case "action":
          return <Modals id={item.id} />;
        default:
          return item[column_key];
      }
    },
    []
  );
  const props: PropsTable<any> = {
    columns,
    renderCell,
    data: data,
    pageInfo: pageInfo,
    basePath: "/referensi/poli",
  };

  return (
    <div className="bg-default-50 p-6 rounded-md flex flex-col gap-6">
      <div className="flex justify-between items-center">
        {JSON.stringify(doctors)}
        <h1 className="font-medium text-xl">Daftar Dokter yang Bertugas</h1>
        <ModalPost />
      </div>
      <TablePoliklinik {...props} />
    </div>
  );
}
