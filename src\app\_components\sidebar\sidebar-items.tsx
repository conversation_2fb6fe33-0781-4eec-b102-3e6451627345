import { SidebarLink, useSidebar } from "@/components/ui/sidebar";
import { findParentSegment, isActiveKey } from "@/libs/find-parent-segment";
import { RouteItem } from "@/types/site-config";
import { Accordion, AccordionItem, cn, Divider } from "@heroui/react";
import { usePathname, useSelectedLayoutSegments } from "next/navigation";
import { motion } from "motion/react";

type Props = { items: RouteItem[] };
export default function SideBaritems({ items }: Props) {
  const pathname = usePathname();
  const segments = useSelectedLayoutSegments();
  const filteredSegments = segments.filter(
    (segment) => !/\(.*\)/.test(segment)
  );

  const homeSegment = pathname === "/" ? "home" : "";

  let selectedSegment = filteredSegments.length
    ? filteredSegments
    : [homeSegment];

  const parent = findParentSegment(items, filteredSegments);

  const { open } = useSidebar();

  return (
    <>
      {items.map((item, idx) => {
        const activeKey = isActiveKey(item.segment, selectedSegment);

        switch (item.type) {
          case "subMenu":
            return (
              <Accordion
                key={item.segment.toString()}
                keepContentMounted
                className="p-0 "
                defaultSelectedKeys={
                  Array.isArray(parent) ? parent[0] : undefined
                }
                itemClasses={{
                  base: "ml-0 group/sidebar",
                  trigger: cn(
                    "w-full rounded-l-lg !no-underline hover:bg-primary-50  dark:hover:bg-slate-700 h-9 gap-2",
                    "group-hover/sidebar:translate-x-1 transition duration-150"
                  ),
                  indicator: "mr-2  ",
                  title: cn(
                    "pr-2 pl-1 font-semibold text-sm",
                    "data-[open=true]:text-primary-500",
                    {
                      "pl-2": !open,
                    }
                  ),
                }}
                selectionMode="multiple"
                showDivider={false}
              >
                <AccordionItem
                  key={item.segment.toString()}
                  aria-label={item.title}
                  title={
                    <div className="inline-flex items-center gap-2 mt-1">
                      {item.icon}
                      <motion.span
                        animate={{
                          display: open ? "flex" : "none",
                          opacity: open ? 1 : 0,
                        }}
                      >
                        {item.title}
                      </motion.span>
                    </div>
                  }
                >
                  <motion.div
                    animate={{
                      paddingLeft: open ? "1rem" : 0,
                    }}
                  >
                    <SideBaritems key={idx} items={item.children ?? []} />
                  </motion.div>
                </AccordionItem>
              </Accordion>
            );

          case "divider":
            return (
              <motion.div
                key={item.title}
                className="font-bold text-xs py-2 text-default-400"
                animate={{
                  marginLeft: open ? "1rem" : "0",
                }}
                initial={{
                  marginLeft: 0,
                }}
              >
                {open ? item.title : <Divider />}
              </motion.div>
            );
          default:
            return <SidebarLink key={idx} item={item} isActive={activeKey} />;
        }
      })}
    </>
  );
}
