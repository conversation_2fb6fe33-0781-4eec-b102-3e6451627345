import React from "react";
import FilterTable from "./_components/filterTable";
import TableObat from "./_components/table";

type Props = {};

function PageBatchInventory({}: Props) {
  const poli: any[] = [];
  return (
    <div className="flex flex-col gap-6">
      <div>
        <FilterTable />
      </div>

      <div className="">
        <TableObat poli={poli} />
      </div>
    </div>
  );
}

export default PageBatchInventory;
