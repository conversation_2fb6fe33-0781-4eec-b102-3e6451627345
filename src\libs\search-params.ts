import { flattenObject, unflattenObject } from "@/libs/utils";

export function setSearchParams<T>(params?: Partial<T>) {
  const searchParams = new URLSearchParams();

  if (params) {
    const flattenParams = flattenObject(params) as Partial<T>;

    Object.keys(flattenParams).forEach((key) => {
      const typedKey = key as keyof T;

      if (flattenParams[typedKey])
        searchParams.set(typedKey.toString(), String(flattenParams[typedKey]));
    });
  }

  return searchParams;
}

export function getSearchParams<T>(
  searchParams: URLSearchParams,
  defaultValues?: T
): Partial<T> {
  const result: Partial<T> = { ...defaultValues }; // Inisialisasi dengan defaultValues

  // Iterate over search params
  (Array.from(searchParams.keys()) as (keyof T)[]).forEach((key) => {
    const value = searchParams.get(key.toString());

    if (value !== null) {
      result[key] = value as T[keyof T];
    }
  });

  return unflattenObject(result) as Partial<T>;
}
