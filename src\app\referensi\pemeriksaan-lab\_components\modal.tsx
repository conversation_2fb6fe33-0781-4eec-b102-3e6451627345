"use client";

import { FormInput } from "@/components/custom/input";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  useDisclosure,
} from "@heroui/react";
import { useFormik } from "formik";
import { Pencil } from "lucide-react";
import { useState } from "react";

import useSWR from "swr";
import ApiResponse from "@/interfaces/api-response";
import { getPoli } from "../actions";
import { poliPost } from "../schema/postPoli";


type payload = {
  poli: string;
};

export default function Modals({ id }: { id: string }) {
  const [isSubmit, setIsSubmit] = useState(false);
  const { isOpen, onOpen, onOpenChange } = useDisclosure(); // state modals

  // swr
  const {
    data: poli,
    isLoading: poliLoading,
    mutate: mutate,
  } = useSWR<ApiResponse<any[]>>(
    id ? ["get_poli", id] : null,
    ([, id]) => getPoli(id as string),
    {
      revalidateIfStale: false,
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      shouldRetryOnError: false,
    }
  );

  const initialValues = {
    poli: "",
  };

  const handleSubmit = (value: typeof initialValues) => {
    setIsSubmit(true);
    mutate();
    alert(JSON.stringify(value));
    setIsSubmit(false);
  };

  const formik = useFormik<payload>({
    initialValues,
    enableReinitialize: true,
    validationSchema: poliPost,
    onSubmit: handleSubmit,
  });

  return (
    <>
      <Tooltip content="Edit Data">
        <Button isIconOnly color="secondary" size="sm" onPress={onOpenChange}>
          <Pencil className="w-4" />
        </Button>
      </Tooltip>
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} backdrop={"blur"}>
        <ModalContent>
          {(onClose) => (
            <>
              <form onSubmit={formik.handleSubmit}>
                <ModalHeader className="flex flex-col gap-1">
                  Tambah Poli
                </ModalHeader>
                <ModalBody className="mb-4">
                  <div className="flex flex-col gap-1">
                    <label htmlFor="poli" className="text-sm ">
                      {" "}
                      Nama Poli
                    </label>
                    <FormInput
                      name={"poli"}
                      isNumeric={false}
                      formik={formik}
                      placeholder="Ketik nama poli"
                    />
                  </div>
                </ModalBody>
                <ModalFooter>
                  <Button
                    className="bg-default-50 border-[1px] border-md"
                    onPress={onClose}
                  >
                    Kembali
                  </Button>
                  <Button color="primary" type="submit" isLoading={isSubmit}>
                    Simpan
                  </Button>
                </ModalFooter>
              </form>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
