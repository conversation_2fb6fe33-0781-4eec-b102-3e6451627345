# 🚀 Next.js Boilerplate with Access Control

Boilerplate ini adalah template dasar untuk proyek **Next.js** yang sudah dilengkapi dengan sistem **Access Control** berbasis permission.

---

## 📦 Setup Project

Ikuti langkah-langkah berikut untuk memulai project ini:

### 1. Ubah Nama Package

Edit file `package.json` dan ubah nilai `"name"` sesuai dengan nama project Anda:

```json
{
  "name": "nama-project-anda"
}
```

### 2. Edit `.npmrc`

Pastikan konfigurasi `.npmrc` sesuai dengan registry atau environment yang Anda gunakan. Contoh:

```ini
registry=https://registry.npmjs.org/

@psi:registry=https://psi-maven.usu.ac.id/repository/psi-npm-releases/
//psi-maven.usu.ac.id/repository/psi-npm-releases/:_authToken={authToken}
always-auth=true
```

### 3. Install Dependencies

Jalankan perintah berikut:

```bash
npm install
```

---

## 🔐 Access Control Guide

Boilerplate ini menyediakan sistem **permission-based access control** yang fleksibel dan mudah digunakan.

### 1. Atur Daftar Permission

Edit file `constant/permission-list.ts`:

```ts
export const PERMISSIONS = [
  "user.read",
  "user.write",
  "role.get",
  "role.delete",
  "role.post",
  // "admin.*",
  // "all",
] as const;
```

> Sesuaikan permission yang dibutuhkan sesuai struktur project Anda.

### 2. Konfigurasi Access Provider

Buka file `access-provider.tsx`, kemudian:

- Gunakan pemission yang dimiliki user dan passing ke props permissions

  ```ts
  import useUser from "...";

  const user = useUser();
  ```

- Pastikan Anda meneruskan data permission ke `AccessProvider`:

  ```tsx
  <AccessProvider permissions={permissionUser}>{children}</AccessProvider>
  ```

---

## 🧩 Komponen `AccessComponent`

`AccessComponent` digunakan untuk mengatur akses ke bagian UI berdasarkan permission yang dimiliki user.

### Contoh Penggunaan

```tsx
<AccessComponent permission={["user.read"]} variant="section">
  <p>Konten ini hanya bisa diakses jika memiliki izin `user.read`</p>
</AccessComponent>
```

### Props

| Prop              | Tipe                                  | Deskripsi                                 |
| ----------------- | ------------------------------------- | ----------------------------------------- |
| `permission`      | `string[]`                            | Daftar permission yang dibutuhkan         |
| `children`        | `ReactNode`                           | Komponen yang dibungkus                   |
| `variant`         | `"page"` \| `"section"` \| `"button"` | Jenis fallback bila tidak ada akses       |
| `message`         | `string`                              | Pesan yang ditampilkan saat akses ditolak |
| `fallbackSection` | `ReactNode`                           | Komponen fallback untuk section           |
| `fallbackPage`    | `ReactNode`                           | Komponen fallback untuk halaman penuh     |
| `fallbackButton`  | `ReactNode`                           | Komponen fallback untuk tombol            |
| `userPermissions` | `string[]` (opsional)                 | Override daftar permission dari context   |

### Perilaku berdasarkan `variant`

- `page`: Menampilkan `<ErrorPage />` saat tidak ada akses.
- `section`: Menampilkan `<ErrorSection />`.
- `button`: Menonaktifkan tombol dengan efek visual dan tooltip.

### Contoh Button Tanpa Akses

```tsx
<AccessComponent permission={["admin.only"]} variant="button">
  <button>Edit Data</button>
</AccessComponent>
```

Jika pengguna tidak memiliki izin, tombol akan:

- Dinonaktifkan (`disabled`)
- Ditampilkan dengan style transparan
- Menampilkan tooltip: `Tidak memiliki akses`

---
