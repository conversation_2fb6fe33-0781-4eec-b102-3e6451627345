"use client";

import { FormInput } from "@/components/custom/input";
import { FormSelect } from "@/components/custom/select";
import {
  addToast,
  AutocompleteItem,
  Button,
  Modal,
  ModalBody,
  ModalContent,
  <PERSON>dal<PERSON>ooter,
  <PERSON>dalHeader,
  Tooltip,
  useDisclosure,
} from "@heroui/react";
import { useFormik } from "formik";
import { Plus } from "lucide-react";
import { useEffect, useState } from "react";
import { getPolilist } from "../../poli/actions";
import { postDoctor } from "../actions";
import { DokterPost } from "../schema/postPoli";

type payload = {
  tenaga_medis: string;
  penugasan: Array<string> | null;
};

export default function ModalPost() {
  const [isSubmit, setIsSubmit] = useState(false);
  const { isOpen, onOpen, onOpenChange } = useDisclosure(); // state modals
  const [loading, setLoading] = useState(false);
  const [poliList, setPoliList] = useState<any[]>([]);

  const getPoli = async () => {
    setLoading(true);
    try {
      const res = (await getPolilist()) as any;
      setPoliList(res);
      return res;
    } catch (error: any) {
      addToast({
        title: "Error",
        color: "danger",
        description: error.message ?? "Terjadi kesalahan",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      getPoli();
    }
  }, [isOpen]);

  const initialValues: payload = {
    tenaga_medis: "",
    penugasan: [],
  };

  const handleSubmit = async (value: typeof initialValues) => {
    //  formik.setSubmitting(true);
    const format = {
      fullName: value.tenaga_medis,
      polyAssociations: (value?.penugasan ?? []).map((item: string) => ({
        id: item,
      })),
    };
    try {
      const res = await postDoctor(format);
      if (res.error) {
        throw new Error(res?.message);
      }
      addToast({
        title: "Berhasil menambahkan data",
        color: "success",
      });
      window.location.reload();
    } catch (error: any) {
      addToast({
        title: "Error",
        color: "danger",
        description: error.message ?? "Terjadi kesalahan",
      });
    } finally {
      formik.setSubmitting(false);
    }
  };

  const formik = useFormik<payload>({
    initialValues,
    enableReinitialize: true,
    validationSchema: DokterPost,
    onSubmit: handleSubmit,
  });

  return (
    <>
      <Tooltip content="Tambah Data">
        <Button
          startContent={<Plus />}
          color="primary"
          size="sm"
          onPress={onOpenChange}
        >
          Poli
        </Button>
      </Tooltip>
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} backdrop={"blur"}>
        <ModalContent>
          {(onClose) => (
            <>
              <form onSubmit={formik.handleSubmit}>
                <ModalHeader className="flex flex-col gap-1">
                  Tambah Dokter
                </ModalHeader>
                <ModalBody className="mb-4">
                  <div className="flex flex-col gap-4">
                    <div className="flex flex-col gap-1">
                      <label htmlFor="poli" className="text-sm ">
                        {" "}
                        Nama Tenaga Medis
                      </label>
                      <FormInput
                        name={"tenaga_medis"}
                        isNumeric={false}
                        formik={formik}
                        placeholder="Ketik nama tenaga medis"
                      />
                    </div>
                    {/* <div className="flex flex-col gap-1">
                      <label htmlFor="jenis" className="text-sm ">
                        {" "}
                        Jenis
                      </label>
                      <FormInput
                        name={"jenis"}
                        isNumeric={false}
                        formik={formik}
                        placeholder="Dokter/Perawat"
                      />
                    </div> */}
                    <div className="flex flex-col gap-1 w-full">
                      <label htmlFor="major_code" className="text-sm ">
                        Penugasan
                      </label>
                      <FormSelect
                        selectionMode="multiple"
                        name="penugasan"
                        classNames={{ base: "bg-white dark:bg-inherit" }}
                        formik={formik}
                        placeholder={`Cari Penugasan`}
                        radius="sm"
                        variant="bordered"
                        isLoading={loading}
                      >
                        {poliList?.map((unit) => (
                          <AutocompleteItem key={unit.id}>
                            {unit?.name}
                          </AutocompleteItem>
                        ))}
                      </FormSelect>
                    </div>
                  </div>
                </ModalBody>
                <ModalFooter>
                  <Button
                    className="bg-default-50 border-[1px] border-md"
                    onPress={() => {
                      onClose();
                      formik.resetForm();
                    }}
                  >
                    Kembali
                  </Button>
                  <Button color="primary" type="submit" isLoading={isSubmit}>
                    Simpan
                  </Button>
                </ModalFooter>
              </form>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
