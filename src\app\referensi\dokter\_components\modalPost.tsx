"use client";

import { FormInput } from "@/components/custom/input";
import {
  AutocompleteItem,
  Button,
  Modal,
  ModalBody,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  useDisclosure,
} from "@heroui/react";
import { useFormik } from "formik";
import { Plus } from "lucide-react";
import { useState } from "react";
import { DokterPost } from "../schema/postPoli";
import { FilterAutocomplete } from "@/components/custom/autocomplete";
import { FormSelect } from "@/components/custom/select";

type payload = {
  tenaga_medis: string;
  jenis: string;
  penugasan: Array<string>;
};

const majors = [
  { key: "1", value: "arsat" },
  { key: "2", value: "teguh" },
  { key: "3", value: "maulana" },
];

export default function ModalPost() {
  const [isSubmit, setIsSubmit] = useState(false);
  const { isOpen, onOpen, onOpenChange } = useDisclosure(); // state modals

  const initialValues = {
    tenaga_medis: "",
    jenis: "",
    penugasan: [""],
  };

  const handleSubmit = (value: typeof initialValues) => {
    setIsSubmit(true);
    alert(JSON.stringify(value));
    setIsSubmit(false);
  };

  const formik = useFormik<payload>({
    initialValues,
    enableReinitialize: true,
    validationSchema: DokterPost,
    onSubmit: handleSubmit,
  });

  return (
    <>
      <Tooltip content="Tambah Data">
        <Button
          startContent={<Plus />}
          color="primary"
          size="sm"
          onPress={onOpenChange}
        >
          Poli
        </Button>
      </Tooltip>
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} backdrop={"blur"}>
        <ModalContent>
          {(onClose) => (
            <>
              <form onSubmit={formik.handleSubmit}>
                <ModalHeader className="flex flex-col gap-1">
                  Tambah Dokter
                </ModalHeader>
                <ModalBody className="mb-4">
                  <div className="flex flex-col gap-4">
                    <div className="flex flex-col gap-1">
                      <label htmlFor="poli" className="text-sm ">
                        {" "}
                        Nama Tenaga Medis
                      </label>
                      <FormInput
                        name={"tenaga_medis"}
                        isNumeric={false}
                        formik={formik}
                        placeholder="Ketik nama tenaga medis"
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <label htmlFor="jenis" className="text-sm ">
                        {" "}
                        Jenis
                      </label>
                      <FormInput
                        name={"jenis"}
                        isNumeric={false}
                        formik={formik}
                        placeholder="Dokter/Perawat"
                      />
                    </div>
                    <div className="flex flex-col gap-1 w-full">
                      <label htmlFor="major_code" className="text-sm ">
                        Penugasan
                      </label>
                      <FormSelect
                        selectionMode="multiple"
                        name="penugasan"
                        classNames={{ base: "bg-white dark:bg-inherit" }}
                        formik={formik}
                        placeholder={`Cari Penugasan`}
                        radius="sm"
                        variant="bordered"
                      >
                        {majors.map((unit) => (
                          <AutocompleteItem key={unit.key}>
                            {unit.value}
                          </AutocompleteItem>
                        ))}
                      </FormSelect>
                    </div>
                  </div>
                </ModalBody>
                <ModalFooter>
                  <Button
                    className="bg-default-50 border-[1px] border-md"
                    onPress={() => {
                      onClose();
                      formik.resetForm();
                    }}
                  >
                    Kembali
                  </Button>
                  <Button color="primary" type="submit" isLoading={isSubmit}>
                    Simpan
                  </Button>
                </ModalFooter>
              </form>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
