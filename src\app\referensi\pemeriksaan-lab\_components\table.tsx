"use client";

import ButtonNavigation from "@/components/custom/buttonNavigation";
import TablePoliklinik from "@/components/table/tablePoliklinik";
import { PropsTable } from "@/interfaces/tables";
import {
  Switch
} from "@heroui/react";
import { useCallback } from "react";

export const users = [
  {
    id: 1,
    name: "<PERSON>",

  },
  {
    id: 2,
    name: "<PERSON>",

  },
  {
    id: 3,
    name: "<PERSON>",

  },
];

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "name",
    label: "Nama Laboratorium",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

export default function TablePemeriksaanLab() {
  const result: any[] = users
  const pageInfo: any = {}

  const renderCell = useCallback(
    (category: any, column_key: any, index: number) => {
      switch (column_key) {
        case "no":
          return index + 1;
        case "name":
          return <div className="">{category.name}</div>;
        case "status":
          return (
            <div className="flex gap-2 justify-center items-center ">
              <Switch
                size="sm"
                defaultSelected={category.status === "active"}
              />
              <p className="capitalize">{category.status}</p>
            </div>
          );
        case "action":
          return <ButtonNavigation href={`/referensi/pemeriksaan-lab/${category.id}`} tema="secondary" icon="eye" tooltip="Lihat Data" isIcon={true} />
        default:
          return category[column_key];
      }
    },
    []
  );
  const props: PropsTable<any> = { columns, renderCell, data: result }
  return (
    <div className="bg-default-50 p-6 rounded-md flex flex-col gap-6">
      <TablePoliklinik {...props} />
    </div>
  );
}
