import React from "react";
import FilterTable from "./_components/filterTable";
import TableObat from "./_components/table";

type Props = {};

function PageBatchInventory({}: Props) {
  return (
    <div className="flex flex-col gap-6">
      <div>
        <FilterTable />
      </div>

      <div className="">
        <TableObat poli={[]} />
      </div>
    </div>
  );
}

export default PageBatchInventory;
