import { Checkbox } from "@heroui/react";
import { Row, Table } from "@tanstack/react-table";
import { ChevronDown, ChevronRight } from "lucide-react";
import { ReactNode } from "react";

type Props = {
  table: Table<any>;
  children?: ReactNode;
  checkbox?: boolean;
  expanded?: boolean;
};

export function TemplateHeader({ table, children, checkbox, expanded }: Props) {
  return (
    <div className="inline-flex justify-start items-center w-full">
      {checkbox && (
        <div className="w-10 inline-flex">
          <Checkbox
            isIndeterminate={table.getIsSomeRowsSelected()}
            isSelected={table.getIsAllRowsSelected()}
            onChange={table.getToggleAllRowsSelectedHandler()}
          />
        </div>
      )}
      {expanded &&
        table.getRowCount() > 0 &&
        table.getRow("0").getCanExpand() && (
          <button
            {...{
              onClick: table.getToggleAllRowsExpandedHandler(),
            }}
          >
            {table.getIsAllRowsExpanded() ? (
              <ChevronDown className="h-5" />
            ) : (
              <ChevronRight className="h-5" />
            )}
          </button>
        )}
      {children}
    </div>
  );
}

type CellProps = {
  row: Row<any>;
  children?: ReactNode;
  checkbox?: boolean;
  expanded?: boolean;
};
export function TemplateCell({ row, children, checkbox, expanded }: CellProps) {
  return (
    <div className="inline-flex justify-start items-center">
      {checkbox && (
        <div className="w-10 inline-flex">
          <Checkbox
            isIndeterminate={row.getIsSomeSelected()}
            isSelected={row.getIsSelected()}
            onChange={row.getToggleSelectedHandler()}
          />
        </div>
      )}
      {expanded ? (
        <div
          className="inline-flex justify-start items-center"
          style={{
            paddingLeft: `${row.depth * 2}rem`,
          }}
        >
          {row.getCanExpand() ? (
            <button
              {...{
                onClick: row.getToggleExpandedHandler(),
                style: { cursor: "pointer" },
              }}
            >
              {row.getIsExpanded() ? (
                <ChevronDown className="h-5" />
              ) : (
                <ChevronRight className="h-5" />
              )}
            </button>
          ) : (
            <>&nbsp;&nbsp;</>
          )}
          &nbsp;
          {children}
        </div>
      ) : (
        children
      )}
    </div>
  );
}
