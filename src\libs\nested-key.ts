export type NestedKeyOf<T, PrevKey extends string = ""> = T extends object
  ? {
      [K in keyof T & string]: T[K] extends Array<infer U>
        ?
            | `${PrevKey}${K}`
            | `${PrevKey}${K}.${number}`
            | `${PrevKey}${K}.${number}.${NestedKeyOf<U, "">}`
        : T[K] extends object
          ? `${PrevKey}${K}` | `${PrevKey}${K}.${NestedKeyOf<T[K], "">}`
          : `${PrevKey}${K}`;
    }[keyof T & string]
  : never;

export const getNestedValue = <T>(obj: T, path: string): any => {
  if (!path) return undefined;

  return path.split(".").reduce((value, key) => {
    if (value === null || value === undefined) return undefined;

    // Deteksi key array menggunakan regex
    const arrayMatch = key.match(/^([a-zA-Z0-9_]+)\[(\d+)\]$/);

    if (arrayMatch) {
      const arrayKey = arrayMatch[1]; // Nama properti array
      const index = parseInt(arrayMatch[2], 10); // Indeks elemen array

      // Pastikan value[arrayKey] adalah array
      if (Array.isArray((value as Record<string, any>)[arrayKey])) {
        return (value as Record<string, any>)[arrayKey][index];
      }

      return undefined;
    }

    // Jika bukan array, akses properti objek
    return (value as Record<string, any>)[key];
  }, obj);
};
