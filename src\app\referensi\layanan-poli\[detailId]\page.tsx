import { getPoliFilterById } from "../actions";
import TableLayananPoliDetail from "./_components/table";

type Props = {
  params: Promise<{
    detailId: string;
  }>;
};

export default async function PageDetailLayanan({ params }: Props) {
  const { detailId: id } = await params;
  const resPoli = getPoliFilterById({ id });

  return (
    <div className="">
      <TableLayananPoliDetail res={resPoli} detailId={id as string} />
    </div>
  );
}
