"use client";

import { FormInput } from "@/components/custom/input";
import {
  addToast,
  Button,
  Modal,
  ModalBody,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  useDisclosure,
} from "@heroui/react";
import { useFormik } from "formik";
import { Plus } from "lucide-react";
import { postObat } from "../schema/obatPost";
import { FilterDatePicker } from "@/components/custom/datepicker";
import { DateValue } from "@internationalized/date";

type payload = {
  bath: string;
  date: DateValue | null;
};

export default function ModalPost() {
  const { isOpen, onOpen, onOpenChange } = useDisclosure(); // state modals

  const initialValues = {
    bath: "",
    date: null,
  };

  const handleSubmit = async (value: payload) => {
    alert(JSON.stringify(value));
    // formik.setSubmitting(true);
    // try {
    //   const res = await postPoli({ name: value.name, active: null });
    //   if (res.error) {
    //     throw new Error(res?.message);
    //   }
    //   addToast({
    //     title: "Berhasil menambahkan data",
    //     color: "success",
    //   });
    //   window.location.reload();
    // } catch (error: any) {
    //   addToast({
    //     title: "Error",
    //     color: "danger",
    //     description: error.message ?? "Terjadi kesalahan",
    //   });
    // } finally {
    //   formik.setSubmitting(false);
    // }
  };

  const formik = useFormik<payload>({
    initialValues,
    enableReinitialize: true,
    validationSchema: postObat,
    onSubmit: handleSubmit,
  });

  return (
    <>
      <Tooltip content="Tambah Data">
        <Button
          startContent={<Plus className="w-4" />}
          color="default"
          className="bg-default-50 border-2"
          size="sm"
          onPress={onOpenChange}
        >
          Batch
        </Button>
      </Tooltip>
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} backdrop={"blur"}>
        <ModalContent>
          {(onClose) => (
            <>
              <form onSubmit={formik.handleSubmit}>
                <ModalHeader className="flex flex-col gap-1">
                  Tambah Batch Inventori
                </ModalHeader>
                <ModalBody className="mb-4">
                  <div className="flex flex-col gap-6">
                    <div className="flex flex-col gap-1">
                      <label htmlFor="poli" className="text-sm ">
                        {" "}
                        No. Batch
                      </label>
                      <FormInput
                        isClearable={false}
                        name={"bath"}
                        isNumeric={false}
                        formik={formik}
                        placeholder="Ketik No. Batch obat masuk"
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <label htmlFor="poli" className="text-sm ">
                        {" "}
                        Tanggal Perolehan
                      </label>
                      <FilterDatePicker name="date" formik={formik} />
                    </div>
                  </div>
                </ModalBody>
                <ModalFooter>
                  <Button
                    className="bg-default-50 border-[1px] border-md"
                    onPress={onClose}
                  >
                    Kembali
                  </Button>
                  <Button
                    color="primary"
                    type="submit"
                    isLoading={formik.isSubmitting}
                  >
                    Simpan
                  </Button>
                </ModalFooter>
              </form>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
