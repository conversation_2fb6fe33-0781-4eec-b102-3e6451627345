import { getSsotok } from "./get-ssotok";

export const downloadFile = async (url: string, filename: string) => {
  const ssotok = await getSsotok();

  fetch(url, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${ssotok}`,
    },
  })
    .then((res) => res.blob())
    .then((file) => {
      const blob = new Blob([file], { type: "images/png" });
      const url = URL.createObjectURL(blob);

      const anchor = document.createElement("a");

      anchor.href = url;
      anchor.download = filename
        .replace(/[<>:"/\\|?*\x00-\x1F]+/g, "_") // hapus special char agar ama file valid
        .trim();
      anchor.click();
    })
    .catch((e) => {
      throw new Error(`Gagal mengunduh file`);
    });
};
