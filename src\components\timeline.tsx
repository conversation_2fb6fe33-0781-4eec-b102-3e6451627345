import { cn } from "@heroui/theme";
import { ReactNode, useMemo } from "react";
import { DynamicIcon as Icon } from "lucide-react/dynamic";

type Props = {
  children?: ReactNode;
  className?: string;
};

export function Timeline({ children, className }: Props) {
  return (
    <ol
      className={cn(
        "relative text-defult-700 border-s border-default-200",
        className,
      )}
    >
      {children}
    </ol>
  );
}

type ItemProps = {
  icon?: ReactNode;
  children: ReactNode;
  className?: string;
  id?: string;
  variant?: "default" | "primary" | "danger" | "warning" | "secondary";
};

export function TimelineItem({
  children,
  id,
  className,
  icon,
  variant,
}: ItemProps) {
  const defaultIcon = useMemo(() => {
    switch (variant) {
      case "primary":
        return <Icon className="h-4" name="check" />;
      case "warning":
        return <Icon className="h-4" name="circle-alert" />;
      case "secondary":
        return <Icon className="h-4" name="info" />;
      case "danger":
        return <Icon className="h-4" name="x" />;
      default:
        return <Icon className="h-4" name="ellipsis" />;
    }
  }, [variant]);

  return (
    <li key={id} className="mb-10 ms-6">
      <span
        className={cn(
          "absolute flex items-center justify-center w-8 h-8 bg-default-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900",
          {
            "bg-primary-200 ": variant === "primary",
          },
          {
            "bg-danger-200": variant === "danger",
          },
          {
            "bg-warning-200": variant === "warning",
          },
          {
            "bg-secondary-200": variant === "secondary",
          },
          className,
        )}
      >
        {icon ?? defaultIcon}
      </span>
      {children}
    </li>
  );
}
