import { RouteItem } from "@/types/site-config";

export type Actions = keyof typeof ACTIONS;
export type Permission = (typeof ACTIONS)[Actions];

const ACTIONS = [
  "*", // semua user yang terdaftar
  "all",
  "role.*",
  "role.get",
  "role.create",
  "role.edit",
  "role.delete",
  "user.*",
  "user.get",
  "user.create",
  "user.edit",
  "user.delete",
] as const;

export function hasPermission(
  userAccess: Permission[],
  permissions: Permission[]
): boolean {
  return (
    permissions.some((r) => userAccess.includes(r)) ||
    userAccess.includes("all")
  );
}

export function hasAccess(route: RouteItem, accessMenu: Permission[]): boolean {
  if (accessMenu.includes("all")) return true;

  if (!route.access) return false; // dianggap public

  if (route.access.includes("*")) {
    return true; // Public page
  }
  return route.access.some((routeAccess) =>
    accessMenu.some((menuAccess) => {
      // if (menuAccess === "all") return true;
      // exact match
      if (routeAccess === menuAccess) return true;

      // wildcard route: employee.*  -> matches employee.get
      if ((routeAccess as string).endsWith(".*")) {
        const prefix = (routeAccess as string).replace(".*", "");
        return (menuAccess as string).startsWith(prefix + ".");
      }

      // wildcard menu: user.create -> matches user.*
      if ((menuAccess as string).endsWith(".*")) {
        const prefix = (menuAccess as string).replace(".*", "");
        return (routeAccess as string).startsWith(prefix + ".");
      }

      return false;
    })
  );
}

export function filterRoutesByAccess(
  routes: RouteItem[],
  accessMenu: Permission[]
): RouteItem[] {
  return routes.reduce<RouteItem[]>((acc, route) => {
    const routeItem: RouteItem = {
      ...route,
    };

    // Handle children (rekursif)
    if (route.children) {
      const filteredChildren = filterRoutesByAccess(route.children, accessMenu);
      if (filteredChildren.length > 0) {
        acc.push({
          ...routeItem,
          children: filteredChildren,
        });
        return acc;
      }
    }

    if (hasAccess(routeItem, accessMenu)) {
      acc.push(routeItem);
    }

    return acc;
  }, []);
}
