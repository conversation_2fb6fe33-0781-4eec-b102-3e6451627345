"use client";

import { FormInput } from "@/components/custom/input";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>ody,
  ModalContent,
  <PERSON>dal<PERSON>oot<PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON>,
  useDisclosure,
} from "@heroui/react";
import { useFormik } from "formik";
import { Plus } from "lucide-react";
import { useState } from "react";
import { poliPost } from "../schema/postPoli";


type payload = {
  poli: string;
};

export default function ModalPost() {
  const [isSubmit, setIsSubmit] = useState(false);
  const { isOpen, onOpen, onOpenChange } = useDisclosure(); // state modals

  const initialValues = {
    poli: "",
  };

  const handleSubmit = (value: typeof initialValues) => {
    setIsSubmit(true);
    alert(JSON.stringify(value));
    setIsSubmit(false);
  };

  const formik = useFormik<payload>({
    initialValues,
    enableReinitialize: true,
    validationSchema: poliPost,
    onSubmit: handleSubmit,
  });

  return (
    <>
      <Tooltip content="Edit Data">
        <Button
          startContent={<Plus />}
          color="primary"
          size="sm"
          onPress={onOpenChange}
        >
          Poli
        </Button>
      </Tooltip>
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} backdrop={"blur"}>
        <ModalContent>
          {(onClose) => (
            <>
              <form onSubmit={formik.handleSubmit}>
                <ModalHeader className="flex flex-col gap-1">
                  Tambah Poli
                </ModalHeader>
                <ModalBody className="mb-4">
                  <div className="flex flex-col gap-1">
                    <label htmlFor="poli" className="text-sm ">
                      {" "}
                      Nama Poli
                    </label>
                    <FormInput
                    isClearable={false}
                      name={"poli"}
                      isNumeric={false}
                      formik={formik}
                      placeholder="Ketik nama poli"
                    />

                  </div>
                </ModalBody>
                <ModalFooter>
                  <Button
                    className="bg-default-50 border-[1px] border-md"
                    // onPress={onClose}
                  >
                    Kembali
                  </Button>
                  <Button color="primary" type="submit" isLoading={isSubmit}>
                    Simpan
                  </Button>
                </ModalFooter>
              </form>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
