"use client";

import ButtonNavigation from "@/components/custom/buttonNavigation";
import TablePoliklinik from "@/components/table/tablePoliklinik";
import { PropsTable } from "@/interfaces/tables";
import {
  Chip,
  Switch
} from "@heroui/react";
import { useCallback } from "react";

export const users = [
  {
    id: 1,
    name: "<PERSON>",
    mahasiswa: "85000",
    pegawai: "20000",
    "kel-pegawai": "10000",
    umum: "20000",
    list: ["<PERSON><PERSON><PERSON><PERSON>", "Pegawai", "Alumni", "Um<PERSON>", "Keluarga Mahasiswa"],
    pensiunan: "20000",
    status: "active",
  },
  {
    id: 2,
    name: "<PERSON>",
    mahasiswa: "85000",
    pegawai: "20000",
    "kel-pegawai": "10000",
    umum: "20000",
    list: ["Mahasisawa", "Pegawai", "Alumni", "Umum", "<PERSON><PERSON><PERSON><PERSON>wa"],
    pensiunan: "20000",
    status: "active",
  },
  {
    id: 3,
    name: "<PERSON>",
    mahasiswa: "85000",
    pegawai: "20000",
    "kel-pegawai": "10000",
    umum: "20000",
    list: ["Mahasisawa", "Pegawai", "Alumni", "Umum", "Keluarga Mahasiswa"],
    pensiunan: "20000",
    status: "active",
  },

];

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "name",
    label: "<PERSON>a <PERSON>i",
  },
  {
    key: "mahasiswa",
    label: "Mahasiswa",
  },
  {
    key: "pegawai",
    label: "Pegawai",
  },
  {
    key: "kel-pegawai",
    label: "Kel.Pegawai",
  },
  {
    key: "umum",
    label: "Umum",
  },
  {
    key: "pensiunan",
    label: "Pensiunan",
  },
  {
    key: "status",
    label: "Status",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

export default function TableDetailPemeriksaanLab() {
  const result: any[] = users
  const pageInfo: any = {}

  const renderCell = useCallback(
    (category: any, column_key: any, index: number) => {
      switch (column_key) {
        case "no":
          return index + 1;
        case "name":
          return (
            <div className="flex flex-col gap-1">
              <p>{category.name}</p>
             <div className="flex items-center gap-2">
               {category.list.map((item: any, index: number) => (
                <Chip key={index} size="sm">
                  {item}
                </Chip>
              ))}
             </div>
            </div>
          )
        case "status":
          return (
            <div className="flex gap-2 justify-end items-center ">
              <Switch
                size="sm"
                defaultSelected={category.status === "active"}
              />
              <p className="capitalize">{category.status}</p>
            </div>
          );
        case "action":
          return <ButtonNavigation href={`/referensi/pemeriksaan-lab/${category.id}/detail`} tema="secondary" icon="eye" tooltip="Lihat Data" isIcon={true} />
        default:
          return category[column_key];
      }
    },
    []
  );
const props: PropsTable<any> = { columns, renderCell, data: result }
  return (
    <div className="bg-default-50 p-6 rounded-md flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <h1 className="font-medium text-xl">Daftar Pemeriksaan Laboratorium Patologi Klinik</h1>
        <ButtonNavigation href={`/referensi/pemeriksaan-lab/tambah-layanan`} tema="light" icon="plus" tooltip="Tambah Data" title="Layanan"  />
      </div>
    <TablePoliklinik {...props} />
    </div>
  );
}
