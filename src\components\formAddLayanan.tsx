"use client";

import { FormInput } from "@/components/custom/input";
import { addToast, Button, Checkbox, CheckboxGroup } from "@heroui/react";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";
import { PATIENT_TYPE_LIST } from "@/constant/constant";
import { layananAddSchema } from "@/app/referensi/layanan-poli/schema/addLayanan";
import { postLayananPoli } from "@/app/referensi/layanan-poli/actions";

type FormValues = {
  laboratorium: string;
  pasienPengguna: string[];
  STUDENT?: string;
  EMPLOYEE?: string;
  FAMILY_OF_EMPLOYEE?: string;
  PUBLIC?: string;
  ALUMNI?: string;
  RETIRED?: string;
};

export default function CompFormAddLayanan({
  mode = "add",
  data,
  id,
}: {
  mode: "add" | "edit";
  data?: any;
  id?: string;
}) {
  const datas = {
    id: "686f936590d98e3c03314975",
    poly_id: "686f46c990d98e3c03314964",
    name: "PROMO !!!",
    tariffs: [
      {
        id: "686f936590d98e3c03314973",
        category: "STUDENT",
        price: 12000,
      },
      {
        id: "686f936590d98e3c03314972",
        category: "FAMILY_OF_EMPLOYEE",
        price: 23000,
      },
      {
        id: "686f936590d98e3c03314974",
        category: "RETIRED",
        price: 30000,
      },
    ],
    active: true,
  };

  const convertFormatApi = {
    poly_id: datas.poly_id,
    laboratorium: datas.name,
    pasienPengguna: datas.tariffs.map((e) => e.category),
    ...datas.tariffs.reduce((acc, e) => {
      acc[e.category] = parseFloat(e.price.toString());
      return acc;
    }, {} as Record<string, number>),
  };

  const initialValues = {
    laboratorium: "",
    pasienPengguna: [] as string[],
  };

  const router = useRouter();

  const handleSubmit = async (values: FormValues) => {
    const format = {
      poly_id: id,
      name: values.laboratorium,
      tariffs: [
        { category: "STUDENT", price: parseInt(values?.STUDENT ?? "0") },
        { category: "EMPLOYEE", price: parseInt(values.EMPLOYEE ?? "0") },
        {
          category: "FAMILY_OF_EMPLOYEE",
          price: parseInt(values.FAMILY_OF_EMPLOYEE ?? "0"),
        },
        { category: "PUBLIC", price: parseInt(values.PUBLIC ?? "0") },
        { category: "ALUMNI", price: parseInt(values.ALUMNI ?? "0") },
        { category: "RETIRED", price: parseInt(values.RETIRED ?? "0") },
      ].filter((item) => item.price > 0),
    };

    formik.setSubmitting(true);
    try {
      const res = await postLayananPoli({ data: format });
      if (res.error) {
        throw new Error(res?.message);
      }
      addToast({
        title: "Berhasil menambahkan data",
        color: "success",
      });
      window.location.reload();
    } catch (error: any) {
      addToast({
        title: "Error",
        color: "danger",
        description: error.message ?? "Terjadi kesalahan",
      });
    } finally {
      formik.setSubmitting(false);
    }
  };

  const formik = useFormik({
    initialValues: mode === "add" ? initialValues : convertFormatApi,
    validationSchema: layananAddSchema,
    onSubmit: handleSubmit,
  });

  const handleCheckboxChange = (selectedValues: string[]) => {
    formik.setFieldValue("pasienPengguna", selectedValues);
  };

  const getTarifFieldName = (key: string): string => {
    switch (key) {
      case PATIENT_TYPE_LIST[0].value:
        return PATIENT_TYPE_LIST[0].label;
      case PATIENT_TYPE_LIST[1].value:
        return PATIENT_TYPE_LIST[1].label;
      case PATIENT_TYPE_LIST[2].value:
        return PATIENT_TYPE_LIST[2].label;
      case PATIENT_TYPE_LIST[3].value:
        return PATIENT_TYPE_LIST[3].label;
      case PATIENT_TYPE_LIST[4].value:
        return PATIENT_TYPE_LIST[4].label;
      case PATIENT_TYPE_LIST[5].value:
        return PATIENT_TYPE_LIST[5].label;
      default:
        return "";
    }
  };

  return (
    <form onSubmit={formik.handleSubmit}>
      <div className="rounded-md w-full flex flex-col justify-center items-center pb-4">
        <div className="flex-col gap-6 flex bg-default-50 lg:w-1/2 w-full p-6 rounded-md">
          <h1 className="text-xl font-medium">
            Form {mode === "add" ? "Tambah" : "Update"} Layanan
          </h1>
          {/* Field Nama Layanan */}
          <div className="flex flex-col gap-1">
            <label htmlFor="laboratorium" className="text-sm">
              Nama Layanan
            </label>
            <FormInput
              name="laboratorium"
              isNumeric={false}
              formik={formik}
              placeholder="Ketik nama laboratorium"
            />
          </div>

          <div className="flex flex-col gap-2">
            <label className="text-sm">Pasien Pengguna Layanan</label>
            <CheckboxGroup
              size="sm"
              value={formik.values.pasienPengguna}
              onChange={handleCheckboxChange}
              name="pasienPengguna"
            >
              {PATIENT_TYPE_LIST.map((item) => (
                <Checkbox key={item.label} value={item.value}>
                  {item.label}
                </Checkbox>
              ))}
            </CheckboxGroup>
            {formik.touched.pasienPengguna && formik.errors.pasienPengguna && (
              <div className="text-red-500 text-sm">
                {formik.errors.pasienPengguna}
              </div>
            )}
          </div>

          <hr />

          {/* Section Tarif */}
          <div className="flex flex-col gap-2">
            <h1 className="font-medium">Tarif</h1>
            {formik.values.pasienPengguna?.length === 0 && (
              <p className="text-sm text-default-500">
                Pilih pasien pengguna layanan terlebih dahulu untuk menentukan
                tarif!
              </p>
            )}
          </div>

          {/* Conditional Tarif Fields */}
          <div className="flex flex-col gap-6">
            {formik.values.pasienPengguna?.length > 0 &&
              formik.values.pasienPengguna?.map((key, index) => (
                <div className="flex flex-col gap-1" key={key}>
                  <label
                    htmlFor={getTarifFieldName(key) as string}
                    className="text-sm capitalize"
                  >
                    {getTarifFieldName(key)}
                  </label>
                  <FormInput
                    name={getTarifFieldName(key) as any}
                    isNumeric={true}
                    formik={formik}
                    placeholder="Rp.0"
                  />
                </div>
              ))}

            <div className="flex items-center gap-4 justify-end mt-4">
              <Button
                className={`bg-default-50 border-[1px] border-md `}
                type="button"
                onPress={() => {
                  formik.resetForm();
                  router.back();
                }}
              >
                Kembali
              </Button>
              <Button
                className={`${!formik.isValid && "bg-default-500"}`}
                color="primary"
                type="submit"
                isLoading={formik.isSubmitting}
                disabled={formik.isSubmitting || !formik.isValid}
              >
                Simpan
              </Button>
            </div>
          </div>
        </div>
      </div>
    </form>
  );
}
