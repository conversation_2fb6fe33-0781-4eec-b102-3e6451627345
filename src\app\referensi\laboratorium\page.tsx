import { Suspense } from "react";
import FilterPoli from "../poli/_components/filter";
import TableLaboratorium from "./_components/table";
import { getLab } from "./actions";
import LoadingTable from "@/components/loadingTable";
import Container from "@/components/ui/container";

export default function PagePoli() {
  const lab = getLab();
  return (
    <Container>
      <div className="flex flex-col gap-4">
        <FilterPoli title="Laboratorium" />
        <Suspense fallback={<LoadingTable />}>
          <TableLaboratorium lab={lab} />
        </Suspense>
      </div>
    </Container>
  );
}
