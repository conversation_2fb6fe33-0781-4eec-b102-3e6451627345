import { cn } from "@heroui/react";
import { ReactNode } from "react";

type Props = {
  label: ReactNode;
  value: ReactNode;
  colon?: boolean;
  bordered?: boolean;
  classNames?: {
    wrapper?: string;
    label?: string;
    value?: string;
  };
};
export default function ListItem({
  label,
  value,
  classNames,
  colon,
  bordered = true,
}: Props) {
  return (
    <div
      className={cn("flex gap-4  py-2  text-sm w-full", classNames?.wrapper, {
        "border-b border-solid border-b-default-300": bordered,
      })}
    >
      <div className={cn("font-medium pl-1 w-36", classNames?.label)}>
        {label}
      </div>
      {colon && <div>:</div>}
      <div className={cn(classNames?.value)}>{value}</div>
    </div>
  );
}
