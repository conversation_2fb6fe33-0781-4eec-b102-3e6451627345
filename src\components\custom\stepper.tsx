import { ReactNode } from "react";
import { cn } from "@heroui/theme";
import { DynamicIcon as Icon } from "lucide-react/dynamic";

type Props = {
  items: {
    title: ReactNode;
    content: ReactNode;
    icon: ReactNode;
  }[];
  currentIndex?: number;
  variant?: "horizontal" | "vertical";
  isError?: boolean;
};
export default function Stepper({
  items,
  currentIndex = -1,
  variant = "horizontal",
  isError,
}: Props) {
  return (
    <div
      className={cn("flex items-center w-full", {
        "flex-col": variant === "vertical",
        "overflow-x-scroll scrollbar-hide": variant === "horizontal",
      })}
    >
      {items.map((item, index) => (
        <div
          key={index}
          className={cn(
            "flex w-full items-center",
            {
              "after:mt-4 after:self-baseline after:w-full after:h-1 after:border-default-300  after:border-b  after:border-3  after:inline-block ":
                index + 1 < items.length && variant === "horizontal",
            },
            {
              "after:border-primary-500": index < currentIndex,
            },
            {
              "items-start": variant === "vertical",
            },
            {
              "min-h-24 relative pb-4":
                index + 1 < items.length && variant === "vertical",
            },
          )}
        >
          {index + 1 < items.length && variant === "vertical" && (
            <div
              className={cn(
                "absolute left-[17px] to-0 bottom-0 h-full w-1 border-b border-3 border-default-300",
                {
                  "border-primary-500": index < currentIndex,
                },
              )}
            />
          )}

          <div
            className={cn("flex", {
              "items-center flex-col": variant === "horizontal",
              "gap-4": variant === "vertical",
            })}
          >
            <div className="flex w-full ">
              {index > 0 && variant === "horizontal" ? (
                <div
                  className={cn(
                    "h-10 flex-auto before:mt-4 before:self-baseline before:w-full before:h-1 before:border-b before:border-default-300 before:border-3  before:inline-block ",
                    {
                      "before:border-primary-500": index <= currentIndex,
                    },
                  )}
                />
              ) : (
                <div className="h-10 flex-auto" />
              )}

              <div
                className={cn(
                  "z-10 flex items-center justify-center w-10 h-10 bg-default-300 rounded-full  shrink-0",
                  { "bg-primary-500": index < currentIndex },
                  {
                    "bg-primary-600": index === currentIndex,
                  },
                  {
                    "bg-danger": index === currentIndex && isError,
                  },
                )}
              >
                {index < currentIndex ? (
                  <Icon className="h-5 text-white" name="check" />
                ) : (
                  <span
                    className={cn("font-medium text-lg", {
                      "text-white": index === currentIndex,
                    })}
                  >
                    {isError && index === currentIndex ? (
                      <Icon className="h-5 text-white" name="x" />
                    ) : (
                      (item.icon ?? index + 1)
                    )}
                  </span>
                )}
              </div>

              {index + 1 < items.length && variant === "horizontal" ? (
                <div
                  className={cn(
                    "h-10 flex-auto after:mt-4   after:self-baseline after:w-full after:h-1 after:border-b after:border-default-300 after:border-3 after:inline-block ",
                    {
                      "after:border-primary-500": index < currentIndex,
                    },
                  )}
                />
              ) : (
                <div className="h-10 flex-auto" />
              )}
            </div>
            <div
              className={cn("flex flex-col  w-max", {
                "items-center": variant === "horizontal",
                "text-danger": index === currentIndex && isError,
              })}
            >
              <div className="text-sm">{item.title}</div>
              <div
                className={cn("text-xs text-default-500", {
                  "text-danger-400": index === currentIndex && isError,
                })}
              >
                {item.content}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
