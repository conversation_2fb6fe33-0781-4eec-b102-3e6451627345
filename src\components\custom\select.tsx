import React, { ChangeEvent, ReactNode, useEffect, useState } from "react";
import { Chip, Input, Select, SelectProps } from "@heroui/react";
import { FormikProps, getIn } from "formik";

import { classNames } from "@/constant/constant";
import { NestedKeyOf } from "@/libs/nested-key";

type FilterSelectProps<T> = {
  name: keyof T;
  formik: FormikProps<T>;
  submitOnChange?: boolean;
  children?: ReactNode;
} & SelectProps;

export function FilterSelect<T>({
  name,
  formik,
  submitOnChange,
  children,
  selectionMode,
  ...res
}: FilterSelectProps<T>) {
  const error = getIn(formik.errors, name);
  const touched = getIn(formik.touched, name);
  const value = getIn(formik.values, name) as string | string[];

  const isValid = !!(touched && error);

  const handleKeyPress = (_e: ChangeEvent<HTMLSelectElement>) => {
    formik.validateForm().then(() => formik.submitForm());
  };

  return (
    <Select
      classNames={classNames.input}
      color={isValid ? "danger" : "default"}
      errorMessage={error?.toString()}
      isInvalid={isValid}
      {...formik.getFieldProps(name)}
      selectedKeys={typeof value === "string" ? [value] : value}
      onChange={(e) => {
        if (selectionMode === "multiple") {
          formik.setFieldValue(name, e.target.value.split(","));
        } else {
          formik.setFieldValue(name, e.target.value);
        }

        if (submitOnChange) {
          handleKeyPress(e);
        }
      }}
      {...res}
    >
      {children}
    </Select>
  );
}

type FormSelectProps<T> = {
  name: NestedKeyOf<T>;
  formik: FormikProps<T>;
  showSeachbar?: boolean;
  children?: ReactNode;
} & SelectProps;

export function FormSelect<T>({
  name,
  formik,
  children,
  selectionMode,
  showSeachbar = false,
  onChange,
  ...res
}: FormSelectProps<T>) {
  const [searchValue, setSearchValue] = useState("");

  const error = getIn(formik.errors, name);
  const touched = getIn(formik.touched, name);
  let value = getIn(formik.values, name) as string | string[];

  const isInValid = !!(error && touched);

  useEffect(() => {
    if (!formik.isSubmitting) return;
    formik.setFieldTouched(name, true, true);
  }, [formik.isSubmitting]);
  
  const processedChildren = React.Children.map(children, (child: any) => {
    if (child.props?.textValue) {
      const isVisible = child.props.textValue
        .toString()
        .toLowerCase()
        .includes(searchValue.toLowerCase());

      return React.cloneElement(child, {
        style: {
          ...child.props.style,
          display: isVisible ? "flex" : "none", // Sembunyikan jika tidak cocok
        },
      });
    }

    return child;
  }).map((child: any) => ({
    ...child,
    key: child.key.split(".$")?.[1] ?? child.key,
  }));

  return (
    <Select
      classNames={{
        ...classNames.input,
        innerWrapper: "overflow-hidden",
      }}
      color={isInValid ? "danger" : "default"}
      errorMessage={error?.toString()}
      isInvalid={isInValid}
      {...formik.getFieldProps(name)}
      isMultiline={selectionMode === "multiple"}
      listboxProps={
        showSeachbar
          ? {
              topContent: (
                <div className="sticky top-0 z-10 bg-white  py-2">
                  <Input
                    placeholder="Cari Pilihan..."
                    value={searchValue}
                    onChange={(e) => setSearchValue(e.target.value)} // Update nilai pencarian
                  />
                </div>
              ),
            }
          : undefined
      }
      renderValue={
        selectionMode === "multiple"
          ? (items) => {
              return (
                <div className="flex flex-wrap gap-1 ">
                  {items.map((item) => (
                    <Chip
                      key={item.key}
                      classNames={{
                        content: "max-w-32 line-clamp-1 ",
                      }}
                      color="primary"
                      size="sm"
                      variant="flat"
                      onClose={() => {
                        formik.setFieldValue(
                          name,
                          items
                            .filter((i) => i.key !== item.key)
                            .map((i) => i.key)
                        );
                      }}
                    >
                      {item.textValue}
                    </Chip>
                  ))}
                </div>
              );
            }
          : undefined
      }
      selectedKeys={typeof value === "string" ? [value] : value}
      selectionMode={selectionMode}
      onChange={(e) => {
        if (onChange) onChange(e);
        if (selectionMode === "multiple") {
          formik.setFieldValue(name, e.target.value.split(","));
        } else {
          formik.setFieldValue(name, e.target.value);
        }
      }}
      onClose={() => setSearchValue("")} // Reset pencarian saat blur
      {...res}
    >
      {processedChildren as any}
    </Select>
  );
}