import * as yup from "yup";

// Schema untuk validasi item obat individual
const stuffItem = yup.object({
  id: yup.string().required("ID  wajib diisi"),
  name: yup.string().required("<PERSON>a barang wajib diisi"),
  quantity: yup
    .number()
    .min(1, "Jumlah Nilai minimal 1")
    .required("Jumlah Nilai wajib diisi"),
  assetValue: yup
    .number()
    .min(1, "Jumlah Nilai minimal 1")
    .required("Jumlah Nilai wajib diisi"),
  typeFund: yup.string().required("Tipe barang wajib diisi"),
  dateAcquired: yup
    .mixed()
    .nullable()
    .required("Tanggal Perolehan wajib diisi"),
  dateExpired: yup
    .mixed()
    .nullable()
    .required("Tanggal Kadaluarsa wajib diisi"),
  location: yup.string().required("Lokasi (penyimpanan barang) wajib diisi"),
});

export const postItem = yup.object({
  title: yup.string().required("Judul wajib diisi"),
  typeDrug: yup.string().required("Jenis wajib diisi"),
  type: yup.string().required("Tipe wajib diisi"),
  fund: yup.string().required("Sumber Dana wajib diisi"),
  receipt: yup.string().required("Bukti Penerimaan wajib diisi"),
  date: yup.mixed().nullable().required("Tanggal wajib diisi"),
  items: yup
    .array()
    .of(stuffItem)
    .min(1, "Minimal harus ada 1 obat")
    .required("Daftar obat wajib diisi"),
});

export type postItem = yup.InferType<typeof postItem>;
