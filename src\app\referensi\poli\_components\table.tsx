"use client";

import TablePoliklinik from "@/components/table/tablePoliklinik";
import { SmoothSwitch } from "@/components/ui/switch-smooth";
import { PropsTable } from "@/interfaces/tables";
import { addToast } from "@heroui/react";
import { use, useCallback, useState } from "react";
import { ResponsePoli, TypePoli } from "../_schema/typePoli";
import { patchtPoli } from "../actions";
import Modals from "./modal";
import ModalPost from "./modalPost";

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "name",
    label: "Nama Poli",
  },
  {
    key: "active",
    label: "Status",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

type Props = {
  poli: any;
  onChangeSwitch?: (id: number) => void;
};

interface Item {
  id: string;
  value: boolean;
}

export default function TablePoli({ poli }: Props) {
  const data: ResponsePoli = use(poli);
  const pageInfo = data?.page;
  const result: TypePoli[] = data?.content ?? [];
  const [items, setItems] = useState<Item[]>([]);

  const upsertItem = useCallback((id: string, value: boolean) => {
    setItems((prevItems) => {
      const existingItemIndex = prevItems.findIndex((item) => item.id === id);

      if (existingItemIndex >= 0) {
        const newItems = [...prevItems];
        newItems[existingItemIndex] = { id, value };
        return newItems;
      }
      return [...prevItems, { id, value }];
    });
  }, []);

  const getItemById = useCallback(
    (id: string): boolean | null => {
      const item = items.find((item) => item.id === id);
      return item ? item.value : null;
    },
    [items]
  );

  const handleChangeSwitch = async ({
    id,
    currentSwitch,
  }: {
    id: string;
    currentSwitch: boolean;
  }) => {
    try {
      const res = await patchtPoli({ active: currentSwitch, id: id });
      return res;
    } catch (error: any) {
      addToast({
        title: "Error",
        color: "danger",
        description: error.message ?? "Terjadi kesalahan",
      });
    }
  };

  const renderCell = useCallback(
    (items: any, column_key: any, index: number) => {
      const currentActive = getItemById(items.id) ?? items.active;
      switch (column_key) {
        case "no":
          return index + 1;
        case "name":
          return <div className="">{items.name}</div>;
        case "active":
          return (
            <div className="flex gap-2  items-center ">
              <SmoothSwitch
                id={items.id}
                isSelected={currentActive}
                onValueChange={(isSelected) => {
                  upsertItem(items.id, isSelected);
                  handleChangeSwitch({
                    id: items.id,
                    currentSwitch: isSelected,
                  });
                }}
              />
              <p className="capitalize">
                {currentActive ? "aktif" : "tidak aktif"}
              </p>
            </div>
          );
        case "action":
          return (
            <Modals id={items.id} active={currentActive} name={items?.name} />
          );
        default:
          return items[column_key];
      }
    },
    [getItemById, upsertItem]
  );

  const props: PropsTable<TypePoli> = {
    columns,
    renderCell,
    data: result,
    pageInfo: pageInfo,
    basePath: "/referensi/poli",
    isRender: true,
  };
  return (
    <div className="bg-default-50 p-6 rounded-md flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <h1 className="font-medium text-xl">Daftar Poli</h1>
        <ModalPost />
      </div>
      <TablePoliklinik {...props} />
    </div>
  );
}
