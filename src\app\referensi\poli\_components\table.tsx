"use client";

import TablePoliklinik from "@/components/table/tablePoliklinik";
import { addToast, Switch } from "@heroui/react";
import { use, useMemo, useState } from "react";
import { ResponsePoli, TypePoli } from "../_schema/typePoli";
import { patchtPoli } from "../actions";
import Modals from "./modal";
import ModalPost from "./modalPost";
import { PropsTable } from "@/interfaces/tables";

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "name",
    label: "Nama Poli",
  },
  {
    key: "active",
    label: "Status",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

type Props = {
  poli: any;
  onChangeSwitch?: (id: number) => void;
};

interface Item {
  id: string;
  value: boolean;
}

export default function TablePoli({ poli }: Props) {
  const data: ResponsePoli = use(poli);
  const pageInfo = data?.page;
  const result: TypePoli[] = data?.content ?? [];
  const [items, setItems] = useState<Item[]>([]);

  const upsertItem = (id: string, value: boolean) => {
    setItems((prevItems) => {
      const existingItemIndex = prevItems.findIndex((item) => item.id === id);

      if (existingItemIndex >= 0) {
        const newItems = [...prevItems];
        newItems[existingItemIndex] = { id, value };
        return newItems;
      }
      return [...prevItems, { id, value }];
    });
  };

  const getItemById = (id: string): boolean | null => {
    const item = items.find((item) => item.id === id);
    return item ? item.value : null;
  };

  const handleChangeSwitch = async ({
    id,
    currentSwitch,
  }: {
    id: string;
    currentSwitch: boolean;
  }) => {
    try {
      const res = await patchtPoli({ active: currentSwitch, id: id });
      return res;
    } catch (error: any) {
      addToast({
        title: "Error",
        color: "danger",
        description: error.message ?? "Terjadi kesalahan",
      });
    }
  };

  const renderCell = (category: any, column_key: any, index: number) => {
    const currentActive = getItemById(category.id) ?? category.active;
    switch (column_key) {
      case "no":
        return index + 1;
      case "name":
        return <div className="">{category.name}</div>;
      case "active":
        return (
          <div className="flex gap-2  items-center ">
            <Switch
              key={`switch-${category.id}-${getItemById(category.id)}`} // Force re-render when state changes
              size="sm"
              defaultSelected={currentActive}
              onValueChange={(isSelected) => {
                upsertItem(category.id, isSelected);
                // handleChangeSwitch({
                //   id: category.id,
                //   currentSwitch: isSelected,
                // });
              }}
            />
            <p className="capitalize">
              {currentActive ? "aktif" : "tidak aktif"}
            </p>
          </div>
        );
      case "action":
        // Gunakan nilai current state untuk modal
        return <Modals id={category.id} active={currentActive} />;
      default:
        return category[column_key];
    }
  };

  const props: PropsTable<TypePoli> = {
    columns,
    renderCell,
    data: result,
    pageInfo: pageInfo,
    basePath: "/referensi/poli",
  };
  return (
    <div className="bg-default-50 p-6 rounded-md flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <h1 className="font-medium text-xl">Daftar Poli</h1>
        {JSON.stringify(getItemById("686f46c990d98e3c03314964"))}
        <ModalPost />
      </div>
      <TablePoliklinik {...props} />
    </div>
  );
}
